"""
增强批量添加功能演示
展示带进度条可视化和性能优化的批量添加功能
"""
import os
import sys
import time
import random
from pathlib import Path
from typing import List, Dict, Any

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from loguru import logger

def create_test_knowledge_data(count: int) -> List[Dict[str, str]]:
    """创建测试知识数据"""
    
    # 金融知识模板
    templates = [
        "股票投资需要关注公司的基本面分析，包括财务状况、盈利能力、成长性等关键指标。",
        "债券投资相对稳定，适合风险偏好较低的投资者，主要收益来源于固定的利息收入。",
        "基金投资通过专业管理实现风险分散，让普通投资者能够参与多元化的投资组合。",
        "期货交易具有杠杆效应，能够放大收益但同时也会放大风险，需要专业的风险管理。",
        "外汇市场是全球最大的金融市场，具有24小时交易和高流动性的特点。",
        "房地产投资信托基金(REITs)为投资者提供了参与房地产市场的便捷投资途径。",
        "量化投资利用数学模型和计算机技术进行投资决策，追求稳定的超额收益。",
        "价值投资策略强调寻找被市场低估的优质公司，通过长期持有获得投资收益。",
        "技术分析通过研究价格走势图表和交易量来预测未来的价格变动趋势。",
        "资产配置是投资组合管理的核心，通过在不同资产类别间分配资金来优化风险收益比。"
    ]
    
    categories = ["股票投资", "债券投资", "基金投资", "期货交易", "外汇投资", 
                 "房地产投资", "量化投资", "价值投资", "技术分析", "资产配置"]
    
    sources = ["投资指南", "金融教材", "研究报告", "市场分析", "投资手册", "专业期刊"]
    
    knowledge_list = []
    
    for i in range(count):
        template_idx = i % len(templates)
        category_idx = i % len(categories)
        source_idx = i % len(sources)
        
        # 添加一些变化使内容更多样化
        variations = [
            f"根据最新研究，{templates[template_idx]}",
            f"专家建议，{templates[template_idx]}",
            f"市场经验表明，{templates[template_idx]}",
            f"投资实践中，{templates[template_idx]}",
            f"从历史数据看，{templates[template_idx]}"
        ]
        
        content = random.choice(variations)
        content += f" (数据编号: {i+1:06d})"
        
        knowledge_item = {
            "content": content,
            "category": categories[category_idx],
            "source": f"{sources[source_idx]} - 第{i+1}条"
        }
        knowledge_list.append(knowledge_item)
    
    return knowledge_list

def demo_basic_batch_with_progress():
    """演示基本的带进度条批量添加"""
    logger.info("=" * 60)
    logger.info("基本批量添加 + 进度条演示")
    logger.info("=" * 60)
    
    try:
        # 初始化RAG系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 创建测试数据
        test_data = create_test_knowledge_data(500)
        logger.info(f"创建了 {len(test_data)} 条测试数据")
        
        # 定义进度回调函数
        def progress_callback(current_batch, total_batches, success_count, failed_count):
            progress = (current_batch / total_batches) * 100
            logger.info(f"进度回调: {progress:.1f}% - 批次 {current_batch}/{total_batches}, "
                       f"成功: {success_count}, 失败: {failed_count}")
        
        # 执行批量添加（显示进度条）
        logger.info("开始批量添加（显示进度条）...")
        start_time = time.time()
        
        success = rag_system.batch_add_knowledge(
            knowledge_list=test_data,
            show_progress=True,  # 显示进度条
            progress_callback=progress_callback,
            enable_cache=True
        )
        
        elapsed_time = time.time() - start_time
        
        # 获取缓存统计
        cache_stats = rag_system.get_cache_stats()
        
        logger.info(f"批量添加完成:")
        logger.info(f"  成功: {success}")
        logger.info(f"  耗时: {elapsed_time:.2f} 秒")
        logger.info(f"  速度: {len(test_data)/elapsed_time:.1f} 项/秒")
        logger.info(f"  缓存统计: {cache_stats}")
        
        return success
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_optimization_modes():
    """演示不同优化模式"""
    logger.info("=" * 60)
    logger.info("优化模式对比演示")
    logger.info("=" * 60)
    
    # 创建测试数据
    test_data = create_test_knowledge_data(300)
    
    modes = [
        ("默认模式", None),
        ("速度优化", "speed"),
        ("内存优化", "memory")
    ]
    
    results = []
    
    for mode_name, mode_type in modes:
        logger.info(f"\n测试 {mode_name}:")
        logger.info("-" * 30)
        
        try:
            # 创建新的RAG系统实例
            rag_system = RAGSystem()
            if not rag_system.initialize():
                logger.error(f"{mode_name} 初始化失败")
                continue
            
            # 应用优化模式
            if mode_type == "speed":
                rag_system.optimize_for_speed()
            elif mode_type == "memory":
                rag_system.optimize_for_memory()
            
            # 执行测试
            start_time = time.time()
            success = rag_system.batch_add_knowledge(
                knowledge_list=test_data,
                show_progress=True,
                enable_cache=True
            )
            elapsed_time = time.time() - start_time
            
            # 获取统计信息
            cache_stats = rag_system.get_cache_stats()
            speed = len(test_data) / elapsed_time if elapsed_time > 0 else 0
            
            result = {
                "mode": mode_name,
                "success": success,
                "time": elapsed_time,
                "speed": speed,
                "cache_stats": cache_stats
            }
            results.append(result)
            
            logger.info(f"结果:")
            logger.info(f"  成功: {success}")
            logger.info(f"  耗时: {elapsed_time:.2f} 秒")
            logger.info(f"  速度: {speed:.1f} 项/秒")
            logger.info(f"  缓存命中率: {cache_stats['hit_rate']:.1f}%")
            
        except Exception as e:
            logger.error(f"{mode_name} 测试失败: {e}")
    
    # 输出对比结果
    logger.info("\n" + "=" * 60)
    logger.info("优化模式对比结果:")
    logger.info("=" * 60)
    logger.info("模式\t\t耗时(s)\t速度(项/s)\t缓存命中率(%)")
    logger.info("-" * 60)
    for result in results:
        if result["success"]:
            logger.info(f"{result['mode']}\t{result['time']:.2f}\t{result['speed']:.1f}\t\t{result['cache_stats']['hit_rate']:.1f}")

def demo_cache_effectiveness():
    """演示缓存效果"""
    logger.info("=" * 60)
    logger.info("缓存效果演示")
    logger.info("=" * 60)
    
    try:
        # 初始化RAG系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 创建包含重复内容的测试数据
        base_templates = [
            "这是一个重复的金融知识内容，用于测试缓存效果。",
            "另一个重复的投资建议内容，验证嵌入向量缓存功能。",
            "第三个重复的市场分析内容，展示缓存命中率提升。"
        ]
        
        test_data = []
        # 添加重复内容
        for i in range(100):
            template = base_templates[i % len(base_templates)]
            test_data.append({
                "content": template,
                "category": "缓存测试",
                "source": f"缓存测试 #{i+1}"
            })
        
        # 添加一些唯一内容
        for i in range(50):
            test_data.append({
                "content": f"唯一内容 #{i+1} - 这是不重复的金融知识内容。",
                "category": "缓存测试",
                "source": f"唯一内容 #{i+1}"
            })
        
        logger.info(f"创建了 {len(test_data)} 条测试数据（包含重复内容）")
        
        # 第一次处理（建立缓存）
        logger.info("第一次处理（建立缓存）...")
        start_time = time.time()
        success1 = rag_system.batch_add_knowledge(
            knowledge_list=test_data,
            show_progress=True,
            enable_cache=True
        )
        time1 = time.time() - start_time
        cache_stats1 = rag_system.get_cache_stats()
        
        # 第二次处理相同数据（利用缓存）
        logger.info("第二次处理相同数据（利用缓存）...")
        start_time = time.time()
        success2 = rag_system.batch_add_knowledge(
            knowledge_list=test_data,
            show_progress=True,
            enable_cache=True
        )
        time2 = time.time() - start_time
        cache_stats2 = rag_system.get_cache_stats()
        
        # 输出对比结果
        logger.info("\n缓存效果对比:")
        logger.info(f"第一次处理: {time1:.2f}s, 缓存命中率: {cache_stats1['hit_rate']:.1f}%")
        logger.info(f"第二次处理: {time2:.2f}s, 缓存命中率: {cache_stats2['hit_rate']:.1f}%")
        
        if time2 < time1:
            speedup = time1 / time2
            logger.info(f"缓存加速比: {speedup:.2f}x")
        
        return success1 and success2
        
    except Exception as e:
        logger.error(f"缓存效果演示失败: {e}")
        return False

def demo_large_dataset():
    """演示大数据集处理"""
    logger.info("=" * 60)
    logger.info("大数据集处理演示")
    logger.info("=" * 60)
    
    try:
        # 初始化RAG系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 为大数据集优化
        rag_system.optimize_for_speed()
        
        # 创建大数据集
        large_dataset = create_test_knowledge_data(2000)
        logger.info(f"创建了 {len(large_dataset)} 条大数据集")
        
        # 详细进度回调
        def detailed_progress_callback(current_batch, total_batches, success_count, failed_count):
            progress = (current_batch / total_batches) * 100
            # 每完成10%显示一次详细信息
            if current_batch % max(1, total_batches // 10) == 0 or current_batch == total_batches:
                logger.info(f"大数据集处理进度: {progress:.1f}% - "
                           f"批次 {current_batch}/{total_batches}, "
                           f"成功: {success_count}, 失败: {failed_count}")
        
        # 执行大数据集处理
        logger.info("开始大数据集处理...")
        start_time = time.time()
        
        success = rag_system.batch_add_knowledge(
            knowledge_list=large_dataset,
            show_progress=True,
            progress_callback=detailed_progress_callback,
            enable_cache=True
        )
        
        elapsed_time = time.time() - start_time
        cache_stats = rag_system.get_cache_stats()
        
        logger.info(f"\n大数据集处理完成:")
        logger.info(f"  数据量: {len(large_dataset)} 条")
        logger.info(f"  成功: {success}")
        logger.info(f"  总耗时: {elapsed_time:.2f} 秒")
        logger.info(f"  平均速度: {len(large_dataset)/elapsed_time:.1f} 项/秒")
        logger.info(f"  缓存统计: {cache_stats}")
        
        return success
        
    except Exception as e:
        logger.error(f"大数据集演示失败: {e}")
        return False

def main():
    """主函数"""
    try:
        logger.info("增强批量添加功能演示程序启动")
        
        # 演示1: 基本批量添加 + 进度条
        demo_basic_batch_with_progress()
        
        # 演示2: 优化模式对比
        demo_optimization_modes()
        
        # 演示3: 缓存效果
        demo_cache_effectiveness()
        
        # 演示4: 大数据集处理
        demo_large_dataset()
        
        logger.info("\n🎉 所有演示完成!")
        
    except KeyboardInterrupt:
        logger.info("用户中断演示")
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
