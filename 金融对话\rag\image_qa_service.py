"""
图像问答服务
基于PDF中的图像内容精确快速地回答用户问题
"""

import os
import sys
import time
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_processor import PDFProcessor
from rag.rag_system import RAGSystem
from idconfig.config import Config

class ImageQAService:
    """图像问答服务"""
    
    def __init__(self):
        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.rag_system = RAGSystem()
        
        # 图像索引缓存
        self._image_index = {}  # {pdf_path: {page_num: [image_info, ...]}}
        self._qa_cache = {}     # 问答结果缓存
        
        logger.info("图像问答服务初始化完成")
    
    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化PDF处理器
            if not self.pdf_processor.initialize():
                logger.error("PDF处理器初始化失败")
                return False
            
            # 初始化RAG系统
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
            
            logger.info("图像问答服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"图像问答服务初始化失败: {e}")
            return False
    
    def answer_question_with_images(self, user_question: str, pdf_path: str = None, 
                                  page_number: int = None) -> Dict[str, Any]:
        """
        基于PDF图像内容回答用户问题
        
        Args:
            user_question: 用户问题
            pdf_path: PDF文件路径（可选，如果不指定则搜索所有已处理的PDF）
            page_number: 页面编号（可选，如果不指定则搜索所有页面）
            
        Returns:
            包含答案和相关图像信息的字典
        """
        try:
            start_time = time.time()
            logger.info(f"🤔 用户问题: {user_question}")
            
            # 生成缓存键
            cache_key = self._generate_qa_cache_key(user_question, pdf_path, page_number)
            if cache_key in self._qa_cache:
                logger.info("✅ 使用缓存的问答结果")
                return self._qa_cache[cache_key]
            
            # 1. 首先尝试从知识库中搜索相关内容
            knowledge_results = self._search_knowledge_base(user_question)
            
            # 2. 查找相关的图像
            relevant_images = self._find_relevant_images(user_question, pdf_path, page_number)
            
            # 3. 对相关图像进行精确分析
            image_analyses = []
            if relevant_images:
                image_analyses = self._analyze_relevant_images(relevant_images, user_question)
            
            # 4. 生成综合答案
            comprehensive_answer = self._generate_comprehensive_answer(
                user_question, knowledge_results, image_analyses
            )
            
            # 5. 构建结果
            result = {
                "question": user_question,
                "answer": comprehensive_answer["answer"],
                "confidence_score": comprehensive_answer["confidence_score"],
                "sources": {
                    "knowledge_base": knowledge_results,
                    "image_analyses": image_analyses
                },
                "processing_time": time.time() - start_time,
                "timestamp": time.time()
            }
            
            # 缓存结果
            self._qa_cache[cache_key] = result
            
            logger.info(f"✅ 问答完成 (耗时: {result['processing_time']:.2f}秒, "
                       f"置信度: {result['confidence_score']:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 图像问答失败: {e}")
            return {
                "question": user_question,
                "answer": "抱歉，处理您的问题时出现了错误。",
                "confidence_score": 0.0,
                "error": str(e)
            }
    
    def _search_knowledge_base(self, question: str) -> List[Dict[str, Any]]:
        """从知识库搜索相关内容"""
        try:
            # 使用RAG系统搜索
            search_results = self.rag_system.search_knowledge(question, top_k=5)
            
            # 过滤出图像相关的结果
            image_related_results = []
            for result in search_results:
                if "图像" in result.get("category", "") or "图" in result.get("content", ""):
                    image_related_results.append(result)
            
            logger.info(f"知识库搜索结果: {len(search_results)} 条，图像相关: {len(image_related_results)} 条")
            return image_related_results
            
        except Exception as e:
            logger.warning(f"知识库搜索失败: {e}")
            return []
    
    def _find_relevant_images(self, question: str, pdf_path: str = None, 
                            page_number: int = None) -> List[Dict[str, Any]]:
        """查找与问题相关的图像"""
        try:
            relevant_images = []
            
            # 如果指定了PDF路径，只搜索该PDF
            if pdf_path:
                if pdf_path in self._image_index:
                    pdf_images = self._image_index[pdf_path]
                    
                    # 如果指定了页面，只搜索该页面
                    if page_number is not None:
                        if page_number in pdf_images:
                            relevant_images.extend(pdf_images[page_number])
                    else:
                        # 搜索所有页面
                        for page_images in pdf_images.values():
                            relevant_images.extend(page_images)
                else:
                    # PDF未在索引中，尝试实时提取
                    logger.info(f"PDF未在索引中，尝试实时提取图像: {pdf_path}")
                    relevant_images = self._extract_images_from_pdf(pdf_path)
            else:
                # 搜索所有已索引的图像
                for pdf_images in self._image_index.values():
                    for page_images in pdf_images.values():
                        relevant_images.extend(page_images)
            
            # 基于问题内容过滤相关图像
            filtered_images = self._filter_images_by_question(relevant_images, question)
            
            logger.info(f"找到相关图像: {len(filtered_images)} 个")
            return filtered_images
            
        except Exception as e:
            logger.warning(f"查找相关图像失败: {e}")
            return []
    
    def _filter_images_by_question(self, images: List[Dict[str, Any]], 
                                 question: str) -> List[Dict[str, Any]]:
        """基于问题内容过滤图像"""
        try:
            question_lower = question.lower()
            filtered_images = []
            
            # 定义问题类型关键词
            chart_keywords = {
                "柱状图": ["柱状", "条形", "bar"],
                "折线图": ["折线", "趋势", "line", "trend"],
                "饼图": ["饼图", "占比", "pie", "比例"],
                "表格": ["表格", "数据表", "table"]
            }
            
            data_keywords = ["数据", "数值", "金额", "收入", "利润", "增长"]
            time_keywords = ["时间", "年度", "季度", "月份", "期间"]
            
            for img_info in images:
                relevance_score = 0
                
                # 检查图像描述中的关键词匹配
                description = img_info.get('description', '').lower()
                extracted_text = img_info.get('extracted_text', '').lower()
                
                # 图表类型匹配
                for chart_type, keywords in chart_keywords.items():
                    if any(keyword in question_lower for keyword in keywords):
                        if chart_type in description or any(keyword in description for keyword in keywords):
                            relevance_score += 0.3
                
                # 数据相关匹配
                if any(keyword in question_lower for keyword in data_keywords):
                    if any(keyword in description or keyword in extracted_text for keyword in data_keywords):
                        relevance_score += 0.2
                
                # 时间相关匹配
                if any(keyword in question_lower for keyword in time_keywords):
                    if any(keyword in description or keyword in extracted_text for keyword in time_keywords):
                        relevance_score += 0.2
                
                # 精确分析结果匹配
                if img_info.get('precise_analysis'):
                    precise_analysis = img_info['precise_analysis']
                    question_relevance = precise_analysis.get('question_relevance', {})
                    if question_relevance.get('relevance_score', 0) > 0.3:
                        relevance_score += question_relevance['relevance_score']
                
                # 设置相关性阈值
                if relevance_score > 0.2:
                    img_info['relevance_score'] = relevance_score
                    filtered_images.append(img_info)
            
            # 按相关性排序
            filtered_images.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            return filtered_images[:10]  # 返回最相关的10个图像
            
        except Exception as e:
            logger.warning(f"图像过滤失败: {e}")
            return images[:5]  # 返回前5个图像作为备选
    
    def _analyze_relevant_images(self, images: List[Dict[str, Any]], 
                               question: str) -> List[Dict[str, Any]]:
        """对相关图像进行精确分析"""
        try:
            analyses = []
            
            for img_info in images:
                try:
                    image_path = img_info.get('image_path')
                    if not image_path or not os.path.exists(image_path):
                        continue
                    
                    # 获取上下文信息
                    context_info = img_info.get('context_info', {})
                    
                    # 使用PDF处理器进行精确分析
                    analysis_result = self.pdf_processor.get_precise_analysis_for_question(
                        image_path, question, context_info
                    )
                    
                    if analysis_result and not analysis_result.get('error'):
                        analysis_result['image_info'] = img_info
                        analyses.append(analysis_result)
                        
                except Exception as e:
                    logger.warning(f"分析图像失败 {img_info.get('image_path', 'unknown')}: {e}")
                    continue
            
            logger.info(f"完成图像分析: {len(analyses)} 个")
            return analyses
            
        except Exception as e:
            logger.warning(f"图像分析失败: {e}")
            return []
    
    def _generate_comprehensive_answer(self, question: str, knowledge_results: List[Dict[str, Any]], 
                                     image_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成综合答案"""
        try:
            answer_parts = []
            confidence_scores = []
            
            # 1. 基于图像分析生成答案
            if image_analyses:
                for analysis in image_analyses[:3]:  # 使用前3个最相关的分析结果
                    targeted_answer = analysis.get('targeted_answer', '')
                    if targeted_answer:
                        answer_parts.append(targeted_answer)
                        confidence_scores.append(analysis.get('confidence_score', 0.5))
                    
                    # 添加具体的数据信息
                    financial_data = analysis.get('financial_data', {})
                    if financial_data.get('values'):
                        values_text = ', '.join([v['raw_text'] for v in financial_data['values'][:3]])
                        answer_parts.append(f"相关数据: {values_text}")
            
            # 2. 基于知识库结果补充答案
            if knowledge_results:
                for result in knowledge_results[:2]:  # 使用前2个知识库结果
                    content = result.get('content', '')
                    if content and len(content) > 20:
                        answer_parts.append(f"相关信息: {content[:200]}")
                        confidence_scores.append(0.6)
            
            # 3. 生成最终答案
            if answer_parts:
                final_answer = "。".join(answer_parts)
                if not final_answer.endswith('。'):
                    final_answer += '。'
                
                # 计算综合置信度
                avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.5
            else:
                final_answer = "抱歉，我无法从相关图像中找到您问题的具体答案。请尝试更具体的问题描述。"
                avg_confidence = 0.1
            
            return {
                "answer": final_answer,
                "confidence_score": min(1.0, avg_confidence)
            }
            
        except Exception as e:
            logger.warning(f"生成综合答案失败: {e}")
            return {
                "answer": "抱歉，生成答案时出现错误。",
                "confidence_score": 0.0
            }
    
    def _extract_images_from_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """从PDF实时提取图像"""
        try:
            # 这里可以调用PDF处理器的图像提取功能
            # 暂时返回空列表，实际实现需要集成快速图像提取器
            logger.info(f"实时提取PDF图像: {pdf_path}")
            return []
            
        except Exception as e:
            logger.warning(f"实时提取PDF图像失败: {e}")
            return []
    
    def _generate_qa_cache_key(self, question: str, pdf_path: str = None, 
                             page_number: int = None) -> str:
        """生成问答缓存键"""
        try:
            import hashlib
            cache_data = f"{question}_{pdf_path}_{page_number}"
            return hashlib.md5(cache_data.encode()).hexdigest()
        except Exception:
            return f"qa_{hash(question)}"
    
    def add_pdf_images_to_index(self, pdf_path: str, images_info: List[Dict[str, Any]]) -> None:
        """将PDF图像添加到索引"""
        try:
            if pdf_path not in self._image_index:
                self._image_index[pdf_path] = {}
            
            for img_info in images_info:
                page_num = img_info.get('page_number', 1)
                if page_num not in self._image_index[pdf_path]:
                    self._image_index[pdf_path][page_num] = []
                
                self._image_index[pdf_path][page_num].append(img_info)
            
            logger.info(f"PDF图像已添加到索引: {pdf_path}, {len(images_info)} 个图像")
            
        except Exception as e:
            logger.warning(f"添加PDF图像到索引失败: {e}")
    
    def clear_cache(self) -> None:
        """清理缓存"""
        self._qa_cache.clear()
        if hasattr(self.pdf_processor, 'precise_analyzer') and self.pdf_processor.precise_analyzer:
            self.pdf_processor.precise_analyzer.clear_cache()
        logger.info("图像问答缓存已清理")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "indexed_pdfs": len(self._image_index),
            "total_images": sum(len(pages) for pages in self._image_index.values() for pages in pages.values()),
            "qa_cache_size": len(self._qa_cache),
            "precise_analyzer_available": hasattr(self.pdf_processor, 'precise_analyzer') and 
                                        self.pdf_processor.precise_analyzer is not None
        }
