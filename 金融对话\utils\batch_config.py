"""
批量处理配置管理
提供灵活的配置选项来优化不同场景下的批量处理性能
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from loguru import logger

@dataclass
class PerformanceProfile:
    """性能配置档案"""
    name: str
    description: str
    batch_size: int
    embedding_batch_size: int
    max_workers: int
    cache_size: int
    memory_threshold: float
    enable_gpu_optimization: bool
    enable_smart_batching: bool
    enable_deduplication: bool
    retry_attempts: int
    retry_delay: float

class BatchConfigManager:
    """批量处理配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/batch_config.json"
        self.config_dir = Path(self.config_file).parent
        self.config_dir.mkdir(exist_ok=True)
        
        # 预定义性能档案
        self.predefined_profiles = {
            "conservative": PerformanceProfile(
                name="conservative",
                description="保守配置 - 适用于低配置机器或稳定性优先场景",
                batch_size=20,
                embedding_batch_size=4,
                max_workers=2,
                cache_size=200,
                memory_threshold=0.5,
                enable_gpu_optimization=False,
                enable_smart_batching=True,
                enable_deduplication=True,
                retry_attempts=3,
                retry_delay=2.0
            ),
            "balanced": PerformanceProfile(
                name="balanced",
                description="平衡配置 - 适用于中等配置机器的通用场景",
                batch_size=50,
                embedding_batch_size=16,
                max_workers=4,
                cache_size=1000,
                memory_threshold=0.65,
                enable_gpu_optimization=True,
                enable_smart_batching=True,
                enable_deduplication=True,
                retry_attempts=3,
                retry_delay=1.0
            ),
            "aggressive": PerformanceProfile(
                name="aggressive",
                description="激进配置 - 适用于高配置机器的高性能场景",
                batch_size=100,
                embedding_batch_size=32,
                max_workers=8,
                cache_size=2000,
                memory_threshold=0.8,
                enable_gpu_optimization=True,
                enable_smart_batching=True,
                enable_deduplication=False,  # 为了速度可以关闭去重
                retry_attempts=2,
                retry_delay=0.5
            ),
            "memory_optimized": PerformanceProfile(
                name="memory_optimized",
                description="内存优化配置 - 适用于内存受限的环境",
                batch_size=30,
                embedding_batch_size=8,
                max_workers=3,
                cache_size=500,
                memory_threshold=0.6,
                enable_gpu_optimization=False,
                enable_smart_batching=True,
                enable_deduplication=True,
                retry_attempts=3,
                retry_delay=1.5
            ),
            "gpu_optimized": PerformanceProfile(
                name="gpu_optimized",
                description="GPU优化配置 - 适用于有强力GPU的环境",
                batch_size=150,
                embedding_batch_size=64,
                max_workers=6,
                cache_size=3000,
                memory_threshold=0.75,
                enable_gpu_optimization=True,
                enable_smart_batching=True,
                enable_deduplication=False,
                retry_attempts=2,
                retry_delay=0.3
            ),
            "large_dataset": PerformanceProfile(
                name="large_dataset",
                description="大数据集配置 - 适用于处理超大数据集",
                batch_size=80,
                embedding_batch_size=24,
                max_workers=6,
                cache_size=5000,
                memory_threshold=0.7,
                enable_gpu_optimization=True,
                enable_smart_batching=True,
                enable_deduplication=True,
                retry_attempts=2,
                retry_delay=0.8
            )
        }
        
        self.current_profile = None
        self.custom_config = {}
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    logger.info(f"已加载配置文件: {self.config_file}")
                    return config
            else:
                logger.info("配置文件不存在，使用默认配置")
                return {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_profile(self, profile_name: str) -> Optional[PerformanceProfile]:
        """获取性能档案"""
        if profile_name in self.predefined_profiles:
            return self.predefined_profiles[profile_name]
        
        # 尝试从配置文件加载自定义档案
        config = self.load_config()
        custom_profiles = config.get("custom_profiles", {})
        
        if profile_name in custom_profiles:
            try:
                profile_data = custom_profiles[profile_name]
                return PerformanceProfile(**profile_data)
            except Exception as e:
                logger.error(f"加载自定义档案失败 {profile_name}: {e}")
        
        return None
    
    def set_profile(self, profile_name: str) -> bool:
        """设置当前性能档案"""
        profile = self.get_profile(profile_name)
        if profile:
            self.current_profile = profile
            logger.info(f"已设置性能档案: {profile_name} - {profile.description}")
            return True
        else:
            logger.error(f"未找到性能档案: {profile_name}")
            return False
    
    def create_custom_profile(self, profile_name: str, **kwargs) -> bool:
        """创建自定义性能档案"""
        try:
            # 使用balanced作为基础模板
            base_profile = self.predefined_profiles["balanced"]
            
            # 更新参数
            profile_data = asdict(base_profile)
            profile_data["name"] = profile_name
            profile_data.update(kwargs)
            
            # 验证参数
            if not self._validate_profile_data(profile_data):
                return False
            
            # 保存到配置文件
            config = self.load_config()
            if "custom_profiles" not in config:
                config["custom_profiles"] = {}
            
            config["custom_profiles"][profile_name] = profile_data
            
            if self.save_config(config):
                logger.info(f"已创建自定义性能档案: {profile_name}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"创建自定义档案失败: {e}")
            return False
    
    def _validate_profile_data(self, profile_data: Dict[str, Any]) -> bool:
        """验证档案数据"""
        try:
            # 检查必需字段
            required_fields = [
                "batch_size", "embedding_batch_size", "max_workers", 
                "cache_size", "memory_threshold"
            ]
            
            for field in required_fields:
                if field not in profile_data:
                    logger.error(f"缺少必需字段: {field}")
                    return False
            
            # 检查数值范围
            if profile_data["batch_size"] < 1 or profile_data["batch_size"] > 1000:
                logger.error("batch_size 必须在 1-1000 之间")
                return False
            
            if profile_data["embedding_batch_size"] < 1 or profile_data["embedding_batch_size"] > 128:
                logger.error("embedding_batch_size 必须在 1-128 之间")
                return False
            
            if profile_data["max_workers"] < 1 or profile_data["max_workers"] > 32:
                logger.error("max_workers 必须在 1-32 之间")
                return False
            
            if profile_data["memory_threshold"] < 0.1 or profile_data["memory_threshold"] > 0.95:
                logger.error("memory_threshold 必须在 0.1-0.95 之间")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证档案数据失败: {e}")
            return False
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        if self.current_profile:
            config = asdict(self.current_profile)
            config.update(self.custom_config)
            return config
        else:
            # 返回默认balanced配置
            return asdict(self.predefined_profiles["balanced"])
    
    def update_config(self, **kwargs) -> None:
        """更新当前配置"""
        self.custom_config.update(kwargs)
        logger.info(f"已更新配置: {kwargs}")
    
    def list_profiles(self) -> Dict[str, str]:
        """列出所有可用的性能档案"""
        profiles = {}
        
        # 预定义档案
        for name, profile in self.predefined_profiles.items():
            profiles[name] = profile.description
        
        # 自定义档案
        config = self.load_config()
        custom_profiles = config.get("custom_profiles", {})
        for name, profile_data in custom_profiles.items():
            profiles[name] = profile_data.get("description", "自定义档案")
        
        return profiles
    
    def auto_detect_optimal_profile(self) -> str:
        """自动检测最优性能档案"""
        try:
            import psutil
            import torch
            
            # 获取系统信息
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            cpu_count = psutil.cpu_count()
            gpu_available = torch.cuda.is_available()
            gpu_memory_gb = 0
            
            if gpu_available:
                gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            # 根据系统配置推荐档案
            if gpu_available and gpu_memory_gb >= 8 and available_gb >= 16:
                recommended = "gpu_optimized"
            elif gpu_available and gpu_memory_gb >= 4 and available_gb >= 8:
                recommended = "aggressive"
            elif available_gb >= 8 and cpu_count >= 4:
                recommended = "balanced"
            elif available_gb >= 4:
                recommended = "memory_optimized"
            else:
                recommended = "conservative"
            
            logger.info(f"系统配置: 内存 {available_gb:.1f}GB, CPU {cpu_count}核, "
                       f"GPU {'是' if gpu_available else '否'}")
            logger.info(f"推荐性能档案: {recommended}")
            
            return recommended
            
        except Exception as e:
            logger.error(f"自动检测失败: {e}")
            return "balanced"
    
    def get_profile_comparison(self) -> Dict[str, Dict[str, Any]]:
        """获取所有档案的对比信息"""
        comparison = {}
        
        for name, profile in self.predefined_profiles.items():
            comparison[name] = {
                "description": profile.description,
                "batch_size": profile.batch_size,
                "embedding_batch_size": profile.embedding_batch_size,
                "max_workers": profile.max_workers,
                "memory_threshold": f"{profile.memory_threshold*100:.0f}%",
                "gpu_optimization": "是" if profile.enable_gpu_optimization else "否",
                "smart_batching": "是" if profile.enable_smart_batching else "否"
            }
        
        return comparison
