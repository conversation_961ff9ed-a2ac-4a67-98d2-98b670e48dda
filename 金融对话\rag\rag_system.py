"""
RAG检索系统
负责文档嵌入、相似度搜索和知识检索功能
"""
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional, Callable
from loguru import logger
import numpy as np
import sys
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import hashlib
import threading

# 进度条相关导入
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    logger.warning("tqdm未安装，将使用简单进度显示")

# 系统信息检测
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil未安装，无法进行系统优化")

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.milvus_manager import MilvusManager
from idconfig.config import Config

class ProgressBar:
    """增强的进度条类"""

    def __init__(self, total: int, desc: str = "Processing", show_rate: bool = True):
        self.total = total
        self.current = 0
        self.desc = desc
        self.show_rate = show_rate
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.use_tqdm = TQDM_AVAILABLE

        if self.use_tqdm:
            self.pbar = tqdm(
                total=total,
                desc=desc,
                unit="items",
                bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {postfix}",
                ncols=100
            )
        else:
            self.pbar = None
            logger.info(f"开始 {desc}: 0/{total} (0.0%)")

    def update(self, n: int = 1, **kwargs):
        """更新进度"""
        self.current += n
        current_time = time.time()

        if self.use_tqdm and self.pbar:
            self.pbar.update(n)
            if kwargs:
                self.pbar.set_postfix(**kwargs)
        else:
            # 每秒最多更新一次，避免日志过多
            if current_time - self.last_update_time >= 1.0 or self.current == self.total:
                progress = (self.current / self.total) * 100
                elapsed = current_time - self.start_time
                rate = self.current / elapsed if elapsed > 0 else 0

                status_info = f"{self.desc}: {self.current}/{self.total} ({progress:.1f}%)"
                if self.show_rate:
                    status_info += f" - {rate:.1f} items/s"

                if kwargs:
                    extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
                    status_info += f" - {extra_info}"

                logger.info(status_info)
                self.last_update_time = current_time

    def set_postfix(self, **kwargs):
        """设置后缀信息"""
        if self.use_tqdm and self.pbar:
            self.pbar.set_postfix(**kwargs)

    def close(self):
        """关闭进度条"""
        if self.use_tqdm and self.pbar:
            self.pbar.close()
        else:
            elapsed = time.time() - self.start_time
            rate = self.current / elapsed if elapsed > 0 else 0
            logger.info(f"{self.desc} 完成: {self.current}/{self.total} - 总耗时 {elapsed:.2f}s - 平均速度 {rate:.1f} items/s")

class RAGSystem:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.milvus_manager = MilvusManager()

        # 批处理配置 - 优化为更高效的批次设置
        self.batch_size = 50  # 知识库批处理大小（增大以提高吞吐量）
        self.embedding_batch_size = 16  # 嵌入向量批处理大小（优化内存使用）
        self.max_workers = 4  # 并行处理线程数（增加并行度）
        
        # 性能优化配置
        self.embedding_cache = {}  # 嵌入向量缓存
        self.cache_size_limit = 1000  # 缓存大小限制
        self.cache_lock = threading.Lock()  # 线程安全的缓存访问
        self.cache_hits = 0  # 缓存命中次数
        self.cache_misses = 0  # 缓存未命中次数
        self.retry_attempts = 3  # 重试次数
        self.retry_delay = 1.0  # 重试延迟（秒）
        
    def initialize(self):
        """初始化RAG系统"""
        try:
            # 初始化嵌入模型
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL}")
            self.embedding_model = SentenceTransformer(self.config.EMBEDDING_MODEL)
            logger.info("嵌入模型加载完成")

            # 验证模型配置
            if not self._validate_model_config():
                return False

            # 初始化Milvus管理器
            if not self.milvus_manager.initialize():
                logger.error("Milvus管理器初始化失败")
                return False

            # 自动优化批处理设置
            self.optimize_batch_settings()

            logger.info("RAG系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            return False

    def optimize_batch_settings(self):
        """根据系统性能自动优化批处理设置"""
        try:
            if not PSUTIL_AVAILABLE:
                logger.warning("psutil不可用，使用默认配置")
                return

            import psutil

            # 获取系统内存信息
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            total_gb = memory.total / (1024**3)
            cpu_count = psutil.cpu_count()

            # 检测GPU信息
            gpu_available = False
            gpu_memory_gb = 0
            gpu_name = "未检测到"

            try:
                import torch
                if torch.cuda.is_available():
                    gpu_available = True
                    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    gpu_name = torch.cuda.get_device_name(0)
                    logger.info(f"检测到GPU: {gpu_name}, 显存: {gpu_memory_gb:.1f}GB")
            except ImportError:
                logger.info("PyTorch未安装，无法检测GPU")

            logger.info(f"系统信息: 总内存 {total_gb:.1f}GB, 可用内存 {available_gb:.1f}GB, CPU核心数 {cpu_count}")

            # 智能调整批处理设置
            if gpu_available and gpu_memory_gb >= 8:
                # 高性能GPU环境
                self.batch_size = min(250, int(available_gb * 25))
                self.embedding_batch_size = min(64, int(gpu_memory_gb * 8))
                self.max_workers = min(12, cpu_count + 2)
                self.cache_size_limit = 3000
                logger.info("检测到高性能GPU环境，使用极速批处理设置")
            elif gpu_available and gpu_memory_gb >= 4:
                # 中等GPU环境
                self.batch_size = min(180, int(available_gb * 18))
                self.embedding_batch_size = min(32, int(gpu_memory_gb * 6))
                self.max_workers = min(10, cpu_count + 1)
                self.cache_size_limit = 2000
                logger.info("检测到中等GPU环境，使用高速批处理设置")
            elif available_gb >= 16:
                # 高内存CPU环境
                self.batch_size = min(150, int(available_gb * 8))
                self.embedding_batch_size = min(24, int(available_gb * 1.5))
                self.max_workers = min(8, cpu_count)
                self.cache_size_limit = 2000
                logger.info("检测到高内存CPU环境，使用大批处理设置")
            elif available_gb >= 8:
                # 中等内存环境
                self.batch_size = min(100, int(available_gb * 10))
                self.embedding_batch_size = min(16, int(available_gb * 2))
                self.max_workers = min(6, cpu_count)
                self.cache_size_limit = 1000
                logger.info("检测到中等内存环境，使用标准批处理设置")
            elif available_gb >= 4:
                # 低内存环境
                self.batch_size = min(60, int(available_gb * 12))
                self.embedding_batch_size = min(8, int(available_gb * 2))
                self.max_workers = min(4, cpu_count)
                self.cache_size_limit = 500
                logger.info("检测到低内存环境，使用保守批处理设置")
            else:
                # 极低内存环境
                self.batch_size = 30
                self.embedding_batch_size = 4
                self.max_workers = min(2, cpu_count)
                self.cache_size_limit = 200
                logger.warning("检测到极低内存环境，使用最小批处理设置")

            # 确保最小值
            self.batch_size = max(10, self.batch_size)
            self.embedding_batch_size = max(4, self.embedding_batch_size)
            self.max_workers = max(1, self.max_workers)

            logger.info(f"优化后的批处理设置:")
            logger.info(f"  - 知识库批处理大小: {self.batch_size}")
            logger.info(f"  - 嵌入向量批处理大小: {self.embedding_batch_size}")
            logger.info(f"  - 最大并行线程数: {self.max_workers}")
            logger.info(f"  - 缓存大小限制: {self.cache_size_limit}")

        except Exception as e:
            logger.warning(f"自动优化批处理设置失败: {e}")
            # 使用保守设置
            self.batch_size = 50
            self.embedding_batch_size = 16
            self.max_workers = 4

    def configure_batch_processing(self, batch_size: int = None, embedding_batch_size: int = None, max_workers: int = None):
        """
        手动配置批处理参数

        Args:
            batch_size: 知识库批处理大小
            embedding_batch_size: 嵌入向量批处理大小
            max_workers: 最大并行线程数
        """
        if batch_size is not None:
            self.batch_size = batch_size
            logger.info(f"知识库批处理大小设置为: {batch_size}")

        if embedding_batch_size is not None:
            self.embedding_batch_size = embedding_batch_size
            logger.info(f"嵌入向量批处理大小设置为: {embedding_batch_size}")

        if max_workers is not None:
            self.max_workers = max_workers
            logger.info(f"最大并行线程数设置为: {max_workers}")

    def get_batch_processing_stats(self) -> Dict[str, Any]:
        """获取批处理配置统计"""
        return {
            "batch_size": self.batch_size,
            "embedding_batch_size": self.embedding_batch_size,
            "max_workers": self.max_workers,
            "model_name": self.config.EMBEDDING_MODEL,
            "vector_dim": self.config.VECTOR_DIM,
            "cache_size": len(self.embedding_cache)
        }

    def _clear_cache_if_needed(self):
        """清理缓存以避免内存溢出"""
        if len(self.embedding_cache) > self.cache_size_limit:
            # 保留最近使用的缓存项
            sorted_items = sorted(self.embedding_cache.items(), key=lambda x: x[1].get('timestamp', 0), reverse=True)
            self.embedding_cache = dict(sorted_items[:self.cache_size_limit // 2])
            logger.info(f"清理缓存，当前缓存大小: {len(self.embedding_cache)}")

    def _get_cached_embedding(self, text: str) -> Optional[List[float]]:
        """从缓存获取嵌入向量"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        if text_hash in self.embedding_cache:
            self.embedding_cache[text_hash]['timestamp'] = time.time()
            return self.embedding_cache[text_hash]['embedding']
        return None

    def _cache_embedding(self, text: str, embedding: List[float]):
        """缓存嵌入向量"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        self.embedding_cache[text_hash] = {
            'embedding': embedding,
            'timestamp': time.time()
        }
        
        self._clear_cache_if_needed()

    def _validate_model_config(self):
        """验证模型配置"""
        try:
            # 测试编码一个简单文本
            test_text = "测试文本"
            test_embedding = self.embedding_model.encode(test_text, normalize_embeddings=True)

            actual_dim = len(test_embedding)
            expected_dim = self.config.VECTOR_DIM

            logger.info(f"模型实际维度: {actual_dim}, 配置维度: {expected_dim}")

            # 检查是否为BGE-M3模型
            if "bge-m3" in self.config.EMBEDDING_MODEL.lower():
                if actual_dim != 1024:
                    logger.error(f"BGE-M3模型应该是1024维，但检测到{actual_dim}维")
                    return False
                if expected_dim != 1024:
                    logger.error("BGE-M3模型需要设置VECTOR_DIM=1024")
                    return False
                logger.info("✓ BGE-M3模型配置验证通过")

            # 检查向量是否归一化
            norm = np.linalg.norm(test_embedding)
            if abs(norm - 1.0) > 0.01:
                logger.warning(f"向量未完全归一化，norm={norm:.4f}")
            else:
                logger.info("✓ 向量归一化验证通过")

            return True

        except Exception as e:
            logger.error(f"模型配置验证失败: {e}")
            return False
    
    def encode_text(self, text: str) -> List[float]:
        """将文本编码为向量"""
        try:
            if not self.embedding_model:
                logger.error("嵌入模型未初始化")
                return []

            # 尝试从缓存获取
            cached_embedding = self._get_cached_embedding(text)
            if cached_embedding:
                logger.info(f"从缓存加载嵌入向量，文本: {text}")
                return cached_embedding

            # 生成嵌入向量
            embedding = self.embedding_model.encode(text, normalize_embeddings=True)

            # 检查向量维度
            actual_dim = len(embedding)
            expected_dim = self.config.VECTOR_DIM

            if actual_dim != expected_dim:
                logger.warning(f"向量维度不匹配: {actual_dim} != {expected_dim}")
                # BGE-M3应该是1024维，如果不匹配说明配置有问题
                if actual_dim == 1024 and expected_dim != 1024:
                    logger.error("检测到BGE-M3模型(1024维)，但配置的VECTOR_DIM不是1024")
                    logger.error("请在config.py中设置 VECTOR_DIM = 1024")
                    return []

                # 如果维度不匹配，进行填充或截断（不推荐）
                if actual_dim < expected_dim:
                    embedding = np.pad(embedding, (0, expected_dim - actual_dim))
                    logger.warning(f"向量已填充到{expected_dim}维")
                else:
                    embedding = embedding[:expected_dim]
                    logger.warning(f"向量已截断到{expected_dim}维")

            # 确保向量是归一化的（BGE-M3推荐使用归一化向量）
            if not hasattr(embedding, 'dtype'):
                embedding = np.array(embedding, dtype=np.float32)

            # 检查向量是否有效
            if not np.isfinite(embedding).all():
                logger.error("生成的向量包含无效值(NaN或Inf)")
                return []

            # 缓存嵌入向量
            self._cache_embedding(text, embedding)
            return embedding.tolist()

        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            return []
    
    def add_knowledge(self, content: str, category: str, source: str = ""):
        """添加知识到知识库"""
        try:
            # 生成嵌入向量
            embedding = self.encode_text(content)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "content": content,
                "category": category,
                "source": source,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_knowledge(data)
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            return False
    
    def add_conversation_history(self, session_id: str, user_query: str, 
                               assistant_response: str, timestamp: int):
        """添加对话历史"""
        try:
            # 将用户查询和助手回复组合作为嵌入内容
            combined_text = f"用户: {user_query}\n助手: {assistant_response}"
            
            # 生成嵌入向量
            embedding = self.encode_text(combined_text)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "timestamp": timestamp,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_history(data)
            
        except Exception as e:
            logger.error(f"添加对话历史失败: {e}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关知识，支持向量检索、全文检索、混合检索"""
        strategy = getattr(self.config, 'RETRIEVAL_STRATEGY', 'vector')
        try:
            if strategy == 'vector':
                return self._vector_search_knowledge(query, top_k)
            elif strategy == 'fulltext':
                return self._fulltext_search_knowledge(query, top_k)
            elif strategy == 'hybrid':
                return self._hybrid_search_knowledge(query, top_k)
            else:
                logger.warning(f"未知检索策略: {strategy}，默认使用向量检索")
                return self._vector_search_knowledge(query, top_k)
        except Exception as e:
            logger.error(f"搜索知识失败: {e}")
            return []

    def _vector_search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """向量检索（召回+重排）"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            # 召回
            results = self.milvus_manager.search_knowledge(query_embedding, top_k)
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            logger.info(f"知识库向量检索完成，召回 {len(filtered_results)} 条相关结果")
            # 重排
            reranked = self._rerank_results(query_embedding, filtered_results)
            return reranked
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []

    def _rerank_results(self, query_embedding, candidates, method=None):
        method = method or getattr(self.config, 'RERANK_METHOD', 'embedding')
        if method == 'embedding':
            return self._embedding_rerank(query_embedding, candidates)
        elif method == 'llm':
            return self._llm_rerank(query, candidates)
        elif method == 'tfidf':
            return self._tfidf_rerank(query, candidates)
        elif method == 'feedback':
            return self._feedback_rerank(query, candidates)
        else:
            logger.warning(f'未知重排方法: {method}，使用默认embedding重排')
            return self._embedding_rerank(query_embedding, candidates)

    def _embedding_rerank(self, query_embedding: List[float], candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于query/content嵌入的余弦相似度重排"""
        try:
            for item in candidates:
                content_emb = self.encode_text(item["content"])
                if query_embedding and content_emb:
                    sim = np.dot(query_embedding, content_emb) / (np.linalg.norm(query_embedding) * np.linalg.norm(content_emb))
                    item["rerank_score"] = float(sim)
                else:
                    item["rerank_score"] = 0.0
            candidates.sort(key=lambda x: x["rerank_score"], reverse=True)
            logger.info("重排完成")
            return candidates
        except Exception as e:
            logger.error(f"基于query/content嵌入的余弦相似度重排失败: {e}")
            return candidates

    def _llm_rerank(self, query: str, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于LLM的重排"""
        try:
            # 假设有一个LLMService类可以处理批量调用
            # 这里需要实际实现一个可以调用LLM的类，例如 OpenAI、智谱、Qwen 等
            # 为了简化，这里使用一个占位符，实际需要替换为具体的LLM调用逻辑
            logger.warning("基于LLM的重排方法尚未完全实现，请确保有LLMService类")
            # 示例：调用一个简单的LLM服务，返回一个分数
            # 实际应用中，这里需要一个真正的LLM服务，例如 OpenAI、智谱、Qwen 等
            # 这里假设有一个LLMService类，它有一个方法可以批量处理 prompt 和返回分数
            # 例如：llm_service.get_similarity_scores(prompts, model_name)

            # 示例：假设有一个简单的LLMService类
            class LLMService:
                def __init__(self, model_name: str):
                    self.model_name = model_name
                    # 实际初始化LLM客户端
                    logger.info(f"初始化LLMService: {model_name}")

                def get_similarity_scores(self, prompts: List[str], model_name: str) -> List[float]:
                    # 模拟调用LLM，返回一个分数
                    # 实际应用中，这里需要一个真正的LLM客户端，例如 OpenAI、智谱、Qwen 等
                    # 这里返回一个简单的分数，例如 prompt 的长度
                    logger.warning(f"模拟调用LLMService: {model_name} 获取相似度分数")
                    return [len(p) for p in prompts] # 示例：返回prompt长度作为分数

            # 假设优先使用本地模型，如果没有则使用默认的
            local_llm_name = getattr(self.config, 'LOCAL_LLM_NAME', 'default')
            if local_llm_name == 'default':
                llm_service = LLMService(self.config.EMBEDDING_MODEL) # 使用嵌入模型名称作为LLM名称
            else:
                llm_service = LLMService(local_llm_name)

            # 准备prompt
            prompts = [f"Query: {query}\nContent: {item['content']}" for item in candidates]
            
            # 批量调用LLM获取分数
            scores = llm_service.get_similarity_scores(prompts, llm_service.model_name)

            # 将分数映射回候选结果
            for i, item in enumerate(candidates):
                item["rerank_score"] = float(scores[i])

            candidates.sort(key=lambda x: x["rerank_score"], reverse=True)
            logger.info("重排完成")
            return candidates
        except Exception as e:
            logger.error(f"基于LLM的重排失败: {e}")
            return candidates

    def _tfidf_rerank(self, query: str, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于TF-IDF的重排"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity

            # 准备文本数据
            all_texts = [f"Query: {query}\nContent: {item['content']}" for item in candidates]
            
            # 使用TfidfVectorizer对所有文本进行向量化
            vectorizer = TfidfVectorizer()
            tfidf_matrix = vectorizer.fit_transform(all_texts)

            # 准备查询向量
            query_vector = vectorizer.transform([f"Query: {query}"])

            # 计算查询与所有候选内容的余弦相似度
            cosine_sim = cosine_similarity(query_vector, tfidf_matrix)

            # 将分数映射回候选结果
            for i, item in enumerate(candidates):
                item["rerank_score"] = float(cosine_sim[0][i])

            candidates.sort(key=lambda x: x["rerank_score"], reverse=True)
            logger.info("重排完成")
            return candidates
        except Exception as e:
            logger.error(f"基于TF-IDF的重排失败: {e}")
            return candidates

    def _feedback_rerank(self, query: str, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于用户反馈的自适应重排"""
        try:
            # 假设有一个反馈分数表，例如内存字典
            feedback_scores = {}
            # 实际应用中，这里需要从数据库或持久化存储加载反馈分数
            # 例如：feedback_scores = load_feedback_scores()

            # 准备prompt
            prompts = [f"Query: {query}\nContent: {item['content']}" for item in candidates]
            
            # 批量调用LLM获取分数
            # 这里需要一个真正的LLM服务，例如 OpenAI、智谱、Qwen 等
            # 这里使用一个占位符，实际需要替换为具体的LLM调用逻辑
            class LLMService:
                def __init__(self, model_name: str):
                    self.model_name = model_name
                    # 实际初始化LLM客户端
                    logger.info(f"初始化LLMService: {model_name}")

                def get_similarity_scores(self, prompts: List[str], model_name: str) -> List[float]:
                    # 模拟调用LLM，返回一个分数
                    # 实际应用中，这里需要一个真正的LLM客户端，例如 OpenAI、智谱、Qwen 等
                    # 这里返回一个简单的分数，例如 prompt 的长度
                    logger.warning(f"模拟调用LLMService: {model_name} 获取相似度分数")
                    return [len(p) for p in prompts] # 示例：返回prompt长度作为分数

            # 假设优先使用本地模型，如果没有则使用默认的
            local_llm_name = getattr(self.config, 'LOCAL_LLM_NAME', 'default')
            if local_llm_name == 'default':
                llm_service = LLMService(self.config.EMBEDDING_MODEL) # 使用嵌入模型名称作为LLM名称
            else:
                llm_service = LLMService(local_llm_name)

            # 批量调用LLM获取分数
            scores = llm_service.get_similarity_scores(prompts, llm_service.model_name)

            # 将分数映射回候选结果
            for i, item in enumerate(candidates):
                # 假设反馈分数也存储在 item 中，或者从 feedback_scores 获取
                # 这里使用一个占位符，实际需要从 feedback_scores 加载
                feedback_score = feedback_scores.get(item["content"], 0.0) # 示例：从内存字典加载反馈分数
                item["rerank_score"] = float(scores[i] * 0.7 + feedback_score * 0.3) # 示例：加权分数

            candidates.sort(key=lambda x: x["rerank_score"], reverse=True)
            logger.info("重排完成")
            return candidates
        except Exception as e:
            logger.error(f"基于用户反馈的自适应重排失败: {e}")
            return candidates

    def _fulltext_search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """全文检索（简单关键词召回）"""
        try:
            all_knowledge = self.milvus_manager.get_all_knowledge()
            if not all_knowledge:
                logger.warning("知识库为空，无法全文检索")
                return []
            # 简单关键词召回（可扩展为BM25/TF-IDF）
            query_lower = query.lower()
            results = []
            for item in all_knowledge:
                content = item.get("content", "")
                if query_lower in content.lower():
                    # 关键词命中，简单分数=命中长度
                    score = content.lower().count(query_lower)
                    results.append({
                        "content": content,
                        "category": item.get("category", ""),
                        "source": item.get("source", ""),
                        "score": score
                    })
            # 按分数排序
            results.sort(key=lambda x: x["score"], reverse=True)
            logger.info(f"全文检索完成，找到 {len(results)} 条相关结果")
            return results[:(top_k or self.config.TOP_K)]
        except Exception as e:
            logger.error(f"全文检索失败: {e}")
            return []

    def _hybrid_search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """混合检索（向量+全文，简单加权重排）"""
        try:
            vector_results = self._vector_search_knowledge(query, top_k)
            fulltext_results = self._fulltext_search_knowledge(query, top_k)
            # 合并去重（以content+source为key）
            result_dict = {}
            for item in vector_results:
                key = (item["content"], item.get("source", ""))
                result_dict[key] = {**item, "vector_score": item["score"], "fulltext_score": 0}
            for item in fulltext_results:
                key = (item["content"], item.get("source", ""))
                if key in result_dict:
                    result_dict[key]["fulltext_score"] = item["score"]
                else:
                    result_dict[key] = {**item, "vector_score": 0, "fulltext_score": item["score"]}
            # 简单加权重排（可调权重）
            alpha = 0.7  # 向量分数权重
            beta = 0.3   # 全文分数权重
            merged = list(result_dict.values())
            for item in merged:
                item["score"] = alpha * item.get("vector_score", 0) + beta * item.get("fulltext_score", 0)
            merged.sort(key=lambda x: x["score"], reverse=True)
            logger.info(f"混合检索完成，找到 {len(merged)} 条相关结果")
            return merged[:(top_k or self.config.TOP_K)]
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            return []
    
    def search_conversation_history(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关对话历史"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索历史对话
            results = self.milvus_manager.search_history(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"历史对话搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {e}")
            return []
    
    def retrieve_context(self, query: str) -> Dict[str, Any]:
        """检索相关上下文信息"""
        try:
            # 搜索知识库
            knowledge_results = self.search_knowledge(query)
            
            # 搜索历史对话
            history_results = self.search_conversation_history(query)
            
            # 组织上下文信息
            context = {
                "knowledge": knowledge_results,
                "history": history_results,
                "query": query
            }
            
            logger.info(f"上下文检索完成 - 知识: {len(knowledge_results)}条, 历史: {len(history_results)}条")
            return context
            
        except Exception as e:
            logger.error(f"检索上下文失败: {e}")
            return {"knowledge": [], "history": [], "query": query}
    
    def batch_add_knowledge(self, knowledge_list: List[Dict[str, str]], batch_size: int = None,
                           progress_callback: Optional[Callable] = None, enable_smart_batching: bool = True,
                           show_progress: bool = True, enable_cache: bool = True):
        """
        批量添加知识到知识库（高度优化版 + 进度条可视化）

        Args:
            knowledge_list: 知识列表
            batch_size: 批处理大小，None时自动选择
            progress_callback: 进度回调函数
            enable_smart_batching: 是否启用智能批处理
            show_progress: 是否显示进度条
            enable_cache: 是否启用嵌入向量缓存
        """
        try:
            if not knowledge_list:
                logger.warning("知识列表为空")
                return False

            # 智能批处理大小选择
            if batch_size is None:
                if enable_smart_batching:
                    batch_size = self._calculate_optimal_batch_size(knowledge_list)
                else:
                    batch_size = self.batch_size

            logger.info(f"开始批量添加 {len(knowledge_list)} 个知识项")
            logger.info(f"使用批处理大小: {batch_size}, 嵌入批处理大小: {self.embedding_batch_size}")
            logger.info(f"并行线程数: {self.max_workers}, 缓存大小: {self.cache_size_limit}")

            total_success, total_fail = 0, 0
            failed_items = []
            start_time = time.time()

            # 预处理：去重和内容验证
            if enable_smart_batching:
                knowledge_list = self._preprocess_knowledge_list(knowledge_list)
                logger.info(f"预处理后剩余 {len(knowledge_list)} 个知识项")

            def process_batch_with_retry(batch_items, batch_idx):
                """带重试机制和智能降级的批次处理"""
                current_batch_size = len(batch_items)

                for attempt in range(self.retry_attempts):
                    try:
                        success_count = self._process_knowledge_batch_optimized(batch_items, batch_idx)
                        if success_count > 0:
                            return success_count, []
                        else:
                            # 如果批次失败且批次较大，尝试分割批次
                            if current_batch_size > 10 and attempt == 0:
                                logger.warning(f"批次 {batch_idx} 失败，尝试分割为更小批次")
                                sub_batches = [batch_items[i:i+10] for i in range(0, len(batch_items), 10)]
                                sub_success = 0
                                sub_failed = []
                                for sub_batch in sub_batches:
                                    sub_count = self._process_knowledge_batch_optimized(sub_batch, f"{batch_idx}-sub")
                                    if sub_count > 0:
                                        sub_success += sub_count
                                    else:
                                        sub_failed.extend(sub_batch)
                                if sub_success > 0:
                                    return sub_success, sub_failed

                            logger.warning(f"批次 {batch_idx} 处理失败，尝试重试 {attempt + 1}/{self.retry_attempts}")
                            if attempt < self.retry_attempts - 1:
                                time.sleep(self.retry_delay * (attempt + 1))

                    except Exception as e:
                        logger.error(f"批次 {batch_idx} 处理异常: {e}")
                        if attempt < self.retry_attempts - 1:
                            time.sleep(self.retry_delay * (attempt + 1))

                # 所有重试都失败
                logger.error(f"批次 {batch_idx} 所有重试都失败")
                return 0, batch_items

            # 智能分批处理
            batches = self._create_smart_batches(knowledge_list, batch_size) if enable_smart_batching else \
                     [knowledge_list[i:i+batch_size] for i in range(0, len(knowledge_list), batch_size)]
            total_batches = len(batches)

            logger.info(f"分为 {total_batches} 个批次进行处理")

            # 创建进度条
            progress_bar = None
            if show_progress:
                progress_bar = ProgressBar(
                    total=len(knowledge_list),
                    desc="批量添加知识",
                    show_rate=True
                )

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(process_batch_with_retry, batch, idx): idx
                          for idx, batch in enumerate(batches, 1)}

                completed_batches = 0
                processed_items = 0

                for future in as_completed(futures):
                    batch_idx = futures[future]
                    success_count, failed_batch = future.result()
                    total_success += success_count
                    if failed_batch:
                        failed_items.extend(failed_batch)

                    completed_batches += 1
                    batch_size_actual = len(batches[batch_idx - 1])
                    processed_items += batch_size_actual

                    # 更新进度条
                    if progress_bar:
                        progress_bar.update(
                            batch_size_actual,
                            success=total_success,
                            failed=len(failed_items),
                            batch=f"{completed_batches}/{total_batches}"
                        )

                    # 计算详细进度信息
                    progress = (completed_batches / total_batches) * 100
                    elapsed_time = time.time() - start_time
                    avg_time_per_batch = elapsed_time / completed_batches if completed_batches > 0 else 0
                    estimated_remaining = avg_time_per_batch * (total_batches - completed_batches)

                    # 计算处理速度
                    items_per_second = processed_items / elapsed_time if elapsed_time > 0 else 0

                    # 每完成10%或最后一个批次时输出详细信息
                    if completed_batches % max(1, total_batches // 10) == 0 or completed_batches == total_batches:
                        logger.info(f"批次进度: {completed_batches}/{total_batches} ({progress:.1f}%)")
                        logger.info(f"处理统计: 成功 {total_success}, 失败 {len(failed_items)}")
                        logger.info(f"处理速度: {items_per_second:.1f} 项/秒, 预计剩余: {estimated_remaining:.1f}秒")

                    # 调用进度回调
                    if progress_callback:
                        progress_callback(completed_batches, total_batches, total_success, len(failed_items))

            # 对失败项进行单条插入重试
            if failed_items:
                logger.warning(f"有 {len(failed_items)} 条知识批量插入失败，尝试单条插入")

                # 为失败项重试创建单独的进度条
                retry_progress_bar = None
                if show_progress and len(failed_items) > 10:  # 只有失败项较多时才显示重试进度条
                    retry_progress_bar = ProgressBar(
                        total=len(failed_items),
                        desc="单条重试",
                        show_rate=True
                    )

                single_start_time = time.time()
                retry_success = 0

                for i, item in enumerate(failed_items):
                    try:
                        if self.add_knowledge(item['content'], item.get('category', 'general'), item.get('source', '')):
                            total_success += 1
                            retry_success += 1
                        else:
                            total_fail += 1
                    except Exception as e:
                        logger.error(f"单条插入失败: {e}")
                        total_fail += 1

                    # 更新重试进度条
                    if retry_progress_bar:
                        retry_progress_bar.update(1, success=retry_success, failed=i + 1 - retry_success)

                    # 每处理100条显示一次进度（当没有进度条时）
                    elif (i + 1) % 100 == 0:
                        single_elapsed = time.time() - single_start_time
                        single_progress = (i + 1) / len(failed_items) * 100
                        logger.info(f"单条重试进度: {single_progress:.1f}% ({i + 1}/{len(failed_items)})")

                # 关闭重试进度条
                if retry_progress_bar:
                    retry_progress_bar.close()

            # 关闭主进度条
            if progress_bar:
                progress_bar.close()

            # 最终统计
            total_time = time.time() - start_time
            success_rate = (total_success / len(knowledge_list)) * 100 if knowledge_list else 0
            processing_speed = len(knowledge_list) / total_time if total_time > 0 else 0

            logger.info("=" * 60)
            logger.info("批量添加完成统计:")
            logger.info(f"  总计处理: {len(knowledge_list)} 条")
            logger.info(f"  成功添加: {total_success} 条")
            logger.info(f"  失败数量: {total_fail} 条")
            logger.info(f"  成功率: {success_rate:.1f}%")
            logger.info(f"  总耗时: {total_time:.2f} 秒")
            logger.info(f"  平均速度: {processing_speed:.1f} 项/秒")
            logger.info(f"  平均每条: {total_time/len(knowledge_list):.3f} 秒")
            if enable_cache and hasattr(self, 'embedding_cache'):
                cache_size = len(getattr(self, 'embedding_cache', {}))
                logger.info(f"  缓存大小: {cache_size} 项")
            logger.info("=" * 60)

            return total_success > 0

        except Exception as e:
            logger.error(f"批量添加知识失败: {e}")
            # 确保进度条被关闭
            if 'progress_bar' in locals() and progress_bar:
                progress_bar.close()
            import traceback
            traceback.print_exc()
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.cache_lock:
            total_requests = self.cache_hits + self.cache_misses
            hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

            return {
                "cache_size": len(self.embedding_cache),
                "cache_limit": self.cache_size_limit,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_rate": hit_rate,
                "total_requests": total_requests
            }

    def clear_cache(self):
        """清空嵌入向量缓存"""
        with self.cache_lock:
            self.embedding_cache.clear()
            self.cache_hits = 0
            self.cache_misses = 0
            logger.info("嵌入向量缓存已清空")

    def optimize_for_speed(self):
        """为速度优化配置"""
        # 增加批处理大小
        self.batch_size = min(self.batch_size * 2, 200)
        self.embedding_batch_size = min(self.embedding_batch_size * 2, 64)

        # 增加缓存大小
        self.cache_size_limit = min(self.cache_size_limit * 2, 5000)

        # 增加并行线程数
        self.max_workers = min(self.max_workers + 2, 12)

        logger.info(f"速度优化完成: batch_size={self.batch_size}, "
                   f"embedding_batch_size={self.embedding_batch_size}, "
                   f"cache_limit={self.cache_size_limit}, max_workers={self.max_workers}")

    def optimize_for_memory(self):
        """为内存优化配置"""
        # 减少批处理大小
        self.batch_size = max(self.batch_size // 2, 20)
        self.embedding_batch_size = max(self.embedding_batch_size // 2, 8)

        # 减少缓存大小
        self.cache_size_limit = max(self.cache_size_limit // 2, 200)

        # 减少并行线程数
        self.max_workers = max(self.max_workers - 1, 2)

        # 清空当前缓存
        self.clear_cache()

        logger.info(f"内存优化完成: batch_size={self.batch_size}, "
                   f"embedding_batch_size={self.embedding_batch_size}, "
                   f"cache_limit={self.cache_size_limit}, max_workers={self.max_workers}")

    def _calculate_optimal_batch_size(self, knowledge_list: List[Dict[str, str]]) -> int:
        """根据内容特征计算最优批处理大小"""
        try:
            if not knowledge_list:
                return self.batch_size

            # 分析内容长度分布
            content_lengths = [len(item.get('content', '')) for item in knowledge_list[:100]]  # 采样前100个
            avg_length = sum(content_lengths) / len(content_lengths)
            max_length = max(content_lengths)

            # 根据内容长度调整批处理大小
            if avg_length > 2000:  # 长文本
                optimal_size = max(10, self.batch_size // 2)
            elif avg_length > 1000:  # 中等文本
                optimal_size = max(20, int(self.batch_size * 0.8))
            else:  # 短文本
                optimal_size = min(self.batch_size * 2, 200)

            logger.info(f"内容分析: 平均长度 {avg_length:.0f}, 最大长度 {max_length}, 优化批处理大小: {optimal_size}")
            return optimal_size

        except Exception as e:
            logger.warning(f"计算最优批处理大小失败: {e}")
            return self.batch_size

    def _preprocess_knowledge_list(self, knowledge_list: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """预处理知识列表：去重、验证、排序"""
        try:
            logger.info("开始预处理知识列表...")

            # 1. 内容验证和清理
            valid_items = []
            for item in knowledge_list:
                content = item.get('content', '').strip()
                if len(content) >= 10:  # 最小内容长度
                    # 清理内容
                    content = ' '.join(content.split())  # 规范化空白字符
                    item['content'] = content
                    valid_items.append(item)

            logger.info(f"内容验证: {len(knowledge_list)} -> {len(valid_items)} 条有效内容")

            # 2. 基于内容哈希的快速去重
            seen_hashes = set()
            unique_items = []
            for item in valid_items:
                content_hash = hashlib.md5(item['content'].encode()).hexdigest()
                if content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_items.append(item)

            logger.info(f"去重处理: {len(valid_items)} -> {len(unique_items)} 条唯一内容")

            # 3. 按内容长度排序（有助于批处理优化）
            unique_items.sort(key=lambda x: len(x['content']))

            return unique_items

        except Exception as e:
            logger.error(f"预处理知识列表失败: {e}")
            return knowledge_list

    def _create_smart_batches(self, knowledge_list: List[Dict[str, str]], batch_size: int) -> List[List[Dict[str, str]]]:
        """创建智能批次：按内容长度和类型分组"""
        try:
            if not knowledge_list:
                return []

            # 按内容长度分组
            short_items = []  # < 500字符
            medium_items = []  # 500-1500字符
            long_items = []   # > 1500字符

            for item in knowledge_list:
                content_len = len(item.get('content', ''))
                if content_len < 500:
                    short_items.append(item)
                elif content_len < 1500:
                    medium_items.append(item)
                else:
                    long_items.append(item)

            logger.info(f"内容分组: 短文本 {len(short_items)}, 中等文本 {len(medium_items)}, 长文本 {len(long_items)}")

            batches = []

            # 为不同长度的内容使用不同的批处理大小
            # 短文本：使用较大批次
            if short_items:
                short_batch_size = min(batch_size * 2, 150)
                for i in range(0, len(short_items), short_batch_size):
                    batches.append(short_items[i:i+short_batch_size])

            # 中等文本：使用标准批次
            if medium_items:
                for i in range(0, len(medium_items), batch_size):
                    batches.append(medium_items[i:i+batch_size])

            # 长文本：使用较小批次
            if long_items:
                long_batch_size = max(batch_size // 2, 10)
                for i in range(0, len(long_items), long_batch_size):
                    batches.append(long_items[i:i+long_batch_size])

            logger.info(f"智能分批完成: 总共 {len(batches)} 个批次")
            return batches

        except Exception as e:
            logger.error(f"创建智能批次失败: {e}")
            # 回退到简单分批
            return [knowledge_list[i:i+batch_size] for i in range(0, len(knowledge_list), batch_size)]

    def _process_knowledge_batch_optimized(self, batch_items: List[Dict[str, str]], batch_idx) -> int:
        """优化的批次处理方法"""
        try:
            if not batch_items:
                return 0

            batch_start_time = time.time()

            # 提取所有文本内容
            texts = [item["content"] for item in batch_items]

            # 批量生成嵌入向量（使用优化的方法）
            embeddings = self.batch_encode_texts_optimized(texts)

            if not embeddings or len(embeddings) != len(texts):
                logger.error(f"批次 {batch_idx}: 嵌入向量生成失败")
                return 0

            # 准备数据
            data = []
            for item, embedding in zip(batch_items, embeddings):
                if embedding and len(embedding) == self.config.VECTOR_DIM:  # 确保嵌入向量有效
                    data.append({
                        "content": item["content"],
                        "category": item.get("category", "general"),
                        "source": item.get("source", ""),
                        "embedding": embedding
                    })

            # 批量插入到Milvus
            if data:
                success = self.milvus_manager.insert_knowledge(data)
                batch_time = time.time() - batch_start_time

                if success:
                    logger.debug(f"批次 {batch_idx}: 成功插入 {len(data)} 条，耗时 {batch_time:.2f}秒")
                    return len(data)
                else:
                    logger.error(f"批次 {batch_idx}: Milvus插入失败")
                    return 0
            else:
                logger.warning(f"批次 {batch_idx}: 没有有效的知识数据")
                return 0

        except Exception as e:
            logger.error(f"批次 {batch_idx} 处理失败: {e}")
            return 0

    def _process_knowledge_batch(self, batch_items: List[Dict[str, str]]) -> int:
        """处理单个批次的知识项（优化版）"""
        try:
            # 提取所有文本内容
            texts = [item["content"] for item in batch_items]

            # 批量生成嵌入向量
            embeddings = self.batch_encode_texts(texts)

            if not embeddings:
                logger.error("批量生成嵌入向量失败")
                return 0

            # 准备数据
            data = []
            for i, (item, embedding) in enumerate(zip(batch_items, embeddings)):
                if embedding:  # 确保嵌入向量有效
                    data.append({
                        "content": item["content"],
                        "category": item.get("category", "general"),
                        "source": item.get("source", ""),
                        "embedding": embedding
                    })

            # 批量插入到Milvus
            if data:
                success = self.milvus_manager.insert_knowledge(data)
                return len(data) if success else 0
            else:
                logger.warning("批次中没有有效的知识数据")
                return 0

        except Exception as e:
            logger.error(f"处理知识批次失败: {e}")
            return 0

    def batch_encode_texts_optimized(self, texts: List[str]) -> List[List[float]]:
        """
        优化的批量编码文本为嵌入向量（线程安全 + 缓存统计）

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        try:
            if not texts:
                return []

            logger.debug(f"开始优化编码 {len(texts)} 个文本")

            # 1. 线程安全地检查缓存
            cached_embeddings = {}
            uncached_texts = []
            uncached_indices = []

            with self.cache_lock:
                for i, text in enumerate(texts):
                    text_hash = hashlib.md5(text.encode()).hexdigest()
                    if text_hash in self.embedding_cache:
                        cached_embeddings[i] = self.embedding_cache[text_hash]
                        self.cache_hits += 1
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i)
                        self.cache_misses += 1

            cache_hit_rate = len(cached_embeddings) / len(texts) * 100
            logger.debug(f"缓存命中率: {cache_hit_rate:.1f}% ({len(cached_embeddings)}/{len(texts)})")

            # 2. 如果所有文本都在缓存中，直接返回
            if not uncached_texts:
                result = [None] * len(texts)
                for idx, embedding in cached_embeddings.items():
                    result[idx] = embedding
                return result

            # 3. 批量编码未缓存的文本
            new_embeddings = []
            if uncached_texts:
                # 使用更小的批次进行编码以优化内存使用
                encode_batch_size = min(self.embedding_batch_size, len(uncached_texts))

                for i in range(0, len(uncached_texts), encode_batch_size):
                    batch_texts = uncached_texts[i:i+encode_batch_size]

                    try:
                        # 生成嵌入向量
                        batch_embeddings = self.embedding_model.encode(
                            batch_texts,
                            normalize_embeddings=True,
                            show_progress_bar=False,
                            convert_to_numpy=True
                        )

                        # 转换为列表格式
                        for embedding in batch_embeddings:
                            if hasattr(embedding, 'tolist'):
                                embedding_list = embedding.tolist()
                            else:
                                embedding_list = list(embedding)

                            # 验证向量有效性
                            if len(embedding_list) == self.config.VECTOR_DIM and all(np.isfinite(embedding_list)):
                                new_embeddings.append(embedding_list)
                            else:
                                logger.warning(f"生成了无效的嵌入向量，维度: {len(embedding_list)}")
                                new_embeddings.append(None)

                    except Exception as e:
                        logger.error(f"编码文本批次失败: {e}")
                        # 为失败的批次添加None
                        new_embeddings.extend([None] * len(batch_texts))

            # 4. 线程安全地更新缓存
            with self.cache_lock:
                for i, text in enumerate(uncached_texts):
                    if i < len(new_embeddings) and new_embeddings[i] is not None:
                        text_hash = hashlib.md5(text.encode()).hexdigest()
                        self.embedding_cache[text_hash] = new_embeddings[i]

                        # 控制缓存大小
                        if len(self.embedding_cache) > self.cache_size_limit:
                            # 删除最旧的缓存项（简单的FIFO策略）
                            oldest_key = next(iter(self.embedding_cache))
                            del self.embedding_cache[oldest_key]

            # 5. 合并结果
            result = [None] * len(texts)

            # 填充缓存的嵌入向量
            for idx, embedding in cached_embeddings.items():
                result[idx] = embedding

            # 填充新生成的嵌入向量
            for i, uncached_idx in enumerate(uncached_indices):
                if i < len(new_embeddings):
                    result[uncached_idx] = new_embeddings[i]

            # 6. 统计结果
            valid_count = sum(1 for emb in result if emb is not None)
            logger.debug(f"编码完成: {valid_count}/{len(texts)} 个有效向量")

            return result

        except Exception as e:
            logger.error(f"优化批量编码失败: {e}")
            # 回退到原始方法
            return self.batch_encode_texts(texts)

    def batch_encode_texts(self, texts: List[str]) -> List[List[float]]:
        """
        批量编码文本为嵌入向量（高度优化版）

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        try:
            if not texts:
                return []

            logger.info(f"开始编码 {len(texts)} 个文本，使用批处理大小: {self.embedding_batch_size}")

            # 检查缓存
            cached_embeddings = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                cached_embedding = self._get_cached_embedding(text)
                if cached_embedding:
                    cached_embeddings.append((i, cached_embedding))
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)

            logger.info(f"缓存命中: {len(cached_embeddings)}/{len(texts)} 个文本")

            # 如果所有文本都在缓存中，直接返回
            if not uncached_texts:
                result = [None] * len(texts)
                for idx, embedding in cached_embeddings:
                    result[idx] = embedding
                return result

            # 编码未缓存的文本
            new_embeddings = self._encode_uncached_texts(uncached_texts)

            # 合并结果
            result = [None] * len(texts)
            
            # 填充缓存的嵌入向量
            for idx, embedding in cached_embeddings:
                result[idx] = embedding
            
            # 填充新编码的嵌入向量
            for i, (text, embedding) in enumerate(zip(uncached_texts, new_embeddings)):
                if embedding:
                    result[uncached_indices[i]] = embedding
                    # 缓存新的嵌入向量
                    self._cache_embedding(text, embedding)

            valid_count = len([r for r in result if r is not None])
            logger.info(f"批量编码完成: {len(texts)} 个文本 -> {valid_count} 个有效向量")
            return result

        except Exception as e:
            logger.error(f"批量编码文本失败: {e}")
            # 回退到单个编码
            logger.info("回退到单个文本编码模式...")
            return self._fallback_encode(texts)

    def _encode_uncached_texts(self, texts: List[str]) -> List[List[float]]:
        """编码未缓存的文本"""
        try:
            if not texts:
                return []

            # 如果文本数量较少，直接处理
            if len(texts) <= self.embedding_batch_size:
                return self._encode_single_batch(texts)

            # 分小批次处理大量文本
            result = []
            total_batches = (len(texts) + self.embedding_batch_size - 1) // self.embedding_batch_size

            for i in range(0, len(texts), self.embedding_batch_size):
                batch_texts = texts[i:i + self.embedding_batch_size]
                current_batch = (i // self.embedding_batch_size) + 1

                logger.info(f"编码子批次 {current_batch}/{total_batches}，包含 {len(batch_texts)} 个文本")

                batch_embeddings = self._encode_single_batch(batch_texts)
                result.extend(batch_embeddings)

                # 添加短暂延迟以避免过载
                time.sleep(0.05)  # 减少延迟时间

            return result

        except Exception as e:
            logger.error(f"编码未缓存文本失败: {e}")
            return [None] * len(texts)

    def _encode_single_batch(self, texts: List[str]) -> List[List[float]]:
        """编码单个批次的文本（优化版）"""
        try:
            start_time = time.time()

            # 使用BGE-M3模型编码
            embeddings = self.embedding_model.encode(
                texts,
                batch_size=len(texts),  # 一次性处理整个批次
                normalize_embeddings=True,
                show_progress_bar=False,
                convert_to_numpy=True,
                device='cpu'  # 明确指定设备以避免GPU内存问题
            )

            encode_time = time.time() - start_time
            logger.info(f"编码 {len(texts)} 个文本耗时: {encode_time:.2f}秒")

            # 转换为列表格式并验证
            result = []
            for embedding in embeddings:
                if hasattr(embedding, 'tolist'):
                    embedding_list = embedding.tolist()
                else:
                    embedding_list = list(embedding)

                # 检查向量是否有效
                if not np.isfinite(embedding).all():
                    logger.error("生成的向量包含无效值(NaN或Inf)")
                    return []

            return result

        except Exception as e:
            logger.error(f"编码单个批次失败: {e}")
            # 回退到更小的批次或单个编码
            if len(texts) > 1:
                logger.info("尝试更小的批次...")
                mid = len(texts) // 2
                left_result = self._encode_single_batch(texts[:mid])
                right_result = self._encode_single_batch(texts[mid:])
                return left_result + right_result
            else:
                # 单个文本编码
                try:
                    embedding = self.encode_text(texts[0])
                    return [embedding] if embedding else [None]
                except Exception as e:
                    logger.error(f"单个文本编码失败: {e}")
                    return [None]

    def _fallback_encode(self, texts: List[str]) -> List[List[float]]:
        """回退编码方法：逐个处理文本（优化版）"""
        result = []
        for i, text in enumerate(texts):
            try:
                # 先检查缓存
                cached_embedding = self._get_cached_embedding(text)
                if cached_embedding:
                    result.append(cached_embedding)
                else:
                    embedding = self.encode_text(text)
                    if embedding:
                        self._cache_embedding(text, embedding)
                    result.append(embedding)
                
                if (i + 1) % 10 == 0:
                    logger.info(f"单个编码进度: {i + 1}/{len(texts)}")
            except Exception as e:
                logger.warning(f"编码文本 {i} 失败: {e}")
                result.append(None)
        return result
