"""
高效批量处理工具
根据系统配置自动优化批量添加知识库的性能
"""
import os
import sys
import time
import psutil
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from loguru import logger
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from rag.pdf_processor import PDFProcessor

@dataclass
class SystemInfo:
    """系统信息"""
    total_memory_gb: float
    available_memory_gb: float
    cpu_count: int
    gpu_available: bool
    gpu_memory_gb: float
    gpu_name: str = ""

@dataclass
class BatchConfig:
    """批处理配置"""
    batch_size: int
    embedding_batch_size: int
    max_workers: int
    cache_size: int
    enable_gpu: bool
    memory_threshold: float

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.processed_items = 0
        self.failed_items = 0
        self.current_memory_usage = 0
        self.peak_memory_usage = 0
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
            
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                self.current_memory_usage = memory_info.rss / (1024**3)  # GB
                self.peak_memory_usage = max(self.peak_memory_usage, self.current_memory_usage)
                time.sleep(5)  # 每5秒检查一次
            except Exception:
                pass
                
    def update_progress(self, processed: int, failed: int = 0):
        """更新进度"""
        self.processed_items = processed
        self.failed_items = failed
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        processing_rate = self.processed_items / elapsed_time if elapsed_time > 0 else 0
        
        return {
            "elapsed_time": elapsed_time,
            "processed_items": self.processed_items,
            "failed_items": self.failed_items,
            "processing_rate": processing_rate,
            "current_memory_gb": self.current_memory_usage,
            "peak_memory_gb": self.peak_memory_usage,
            "success_rate": (self.processed_items / (self.processed_items + self.failed_items)) * 100 
                           if (self.processed_items + self.failed_items) > 0 else 0
        }

class SmartBatchProcessor:
    """智能批量处理器"""
    
    def __init__(self):
        self.system_info = self._detect_system_info()
        self.batch_config = self._calculate_optimal_config()
        self.rag_system = None
        self.pdf_processor = None
        self.performance_monitor = PerformanceMonitor()
        
    def _detect_system_info(self) -> SystemInfo:
        """检测系统信息"""
        try:
            # 内存信息
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024**3)
            available_memory_gb = memory.available / (1024**3)
            cpu_count = psutil.cpu_count()
            
            # GPU信息
            gpu_available = False
            gpu_memory_gb = 0
            gpu_name = ""
            
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_available = True
                    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    gpu_name = torch.cuda.get_device_name(0)
            except ImportError:
                pass
                
            system_info = SystemInfo(
                total_memory_gb=total_memory_gb,
                available_memory_gb=available_memory_gb,
                cpu_count=cpu_count,
                gpu_available=gpu_available,
                gpu_memory_gb=gpu_memory_gb,
                gpu_name=gpu_name
            )
            
            logger.info(f"系统检测完成:")
            logger.info(f"  内存: {available_memory_gb:.1f}GB / {total_memory_gb:.1f}GB")
            logger.info(f"  CPU: {cpu_count} 核心")
            if gpu_available:
                logger.info(f"  GPU: {gpu_name} ({gpu_memory_gb:.1f}GB)")
            else:
                logger.info(f"  GPU: 未检测到")
                
            return system_info
            
        except Exception as e:
            logger.error(f"系统信息检测失败: {e}")
            # 返回默认配置
            return SystemInfo(
                total_memory_gb=8.0,
                available_memory_gb=4.0,
                cpu_count=4,
                gpu_available=False,
                gpu_memory_gb=0
            )
    
    def _calculate_optimal_config(self) -> BatchConfig:
        """计算最优配置"""
        info = self.system_info
        
        # 基础配置
        if info.gpu_available and info.gpu_memory_gb >= 8:
            # 高性能GPU配置
            batch_size = min(200, int(info.available_memory_gb * 25))
            embedding_batch_size = min(64, int(info.gpu_memory_gb * 8))
            max_workers = min(12, info.cpu_count + 2)
            cache_size = 3000
            enable_gpu = True
            memory_threshold = 0.8
        elif info.gpu_available and info.gpu_memory_gb >= 4:
            # 中等GPU配置
            batch_size = min(150, int(info.available_memory_gb * 20))
            embedding_batch_size = min(32, int(info.gpu_memory_gb * 6))
            max_workers = min(8, info.cpu_count + 1)
            cache_size = 2000
            enable_gpu = True
            memory_threshold = 0.75
        elif info.available_memory_gb >= 16:
            # 高内存CPU配置
            batch_size = min(120, int(info.available_memory_gb * 8))
            embedding_batch_size = min(24, int(info.available_memory_gb * 1.5))
            max_workers = min(8, info.cpu_count)
            cache_size = 2000
            enable_gpu = False
            memory_threshold = 0.7
        elif info.available_memory_gb >= 8:
            # 标准配置
            batch_size = min(80, int(info.available_memory_gb * 10))
            embedding_batch_size = min(16, int(info.available_memory_gb * 2))
            max_workers = min(6, info.cpu_count)
            cache_size = 1000
            enable_gpu = False
            memory_threshold = 0.65
        else:
            # 低配置
            batch_size = min(40, int(info.available_memory_gb * 10))
            embedding_batch_size = min(8, int(info.available_memory_gb * 2))
            max_workers = min(4, info.cpu_count)
            cache_size = 500
            enable_gpu = False
            memory_threshold = 0.6
            
        # 确保最小值
        batch_size = max(10, batch_size)
        embedding_batch_size = max(4, embedding_batch_size)
        max_workers = max(1, max_workers)
        
        config = BatchConfig(
            batch_size=batch_size,
            embedding_batch_size=embedding_batch_size,
            max_workers=max_workers,
            cache_size=cache_size,
            enable_gpu=enable_gpu,
            memory_threshold=memory_threshold
        )
        
        logger.info(f"最优配置计算完成:")
        logger.info(f"  批处理大小: {config.batch_size}")
        logger.info(f"  嵌入批处理大小: {config.embedding_batch_size}")
        logger.info(f"  最大工作线程: {config.max_workers}")
        logger.info(f"  缓存大小: {config.cache_size}")
        logger.info(f"  启用GPU: {config.enable_gpu}")
        logger.info(f"  内存阈值: {config.memory_threshold * 100:.0f}%")
        
        return config
    
    def initialize(self) -> bool:
        """初始化处理器"""
        try:
            logger.info("初始化智能批量处理器...")
            
            # 初始化RAG系统
            self.rag_system = RAGSystem()
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
                
            # 应用优化配置
            self.rag_system.batch_size = self.batch_config.batch_size
            self.rag_system.embedding_batch_size = self.batch_config.embedding_batch_size
            self.rag_system.max_workers = self.batch_config.max_workers
            self.rag_system.cache_size_limit = self.batch_config.cache_size
            
            # 初始化PDF处理器
            self.pdf_processor = PDFProcessor()
            if not self.pdf_processor.initialize():
                logger.error("PDF处理器初始化失败")
                return False
                
            logger.info("智能批量处理器初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False

    def batch_add_knowledge_smart(self, knowledge_list: List[Dict[str, str]],
                                 progress_callback: Optional[Callable] = None) -> bool:
        """
        智能批量添加知识到知识库

        Args:
            knowledge_list: 知识列表
            progress_callback: 进度回调函数 callback(current, total, success_count, total_items)

        Returns:
            是否成功
        """
        try:
            if not knowledge_list:
                logger.warning("知识列表为空")
                return False

            if not self.rag_system:
                logger.error("RAG系统未初始化")
                return False

            logger.info(f"开始智能批量处理 {len(knowledge_list)} 个知识项")

            # 启动性能监控
            self.performance_monitor.start_monitoring()

            def progress_wrapper(current, total, success_count, total_items):
                """包装进度回调，添加性能监控"""
                self.performance_monitor.update_progress(success_count)

                # 检查内存使用情况
                memory_usage = self.performance_monitor.current_memory_usage
                memory_threshold_gb = self.system_info.total_memory_gb * self.batch_config.memory_threshold

                if memory_usage > memory_threshold_gb:
                    logger.warning(f"内存使用过高: {memory_usage:.1f}GB > {memory_threshold_gb:.1f}GB")
                    # 可以在这里实施内存优化策略

                # 调用用户提供的回调
                if progress_callback:
                    progress_callback(current, total, success_count, total_items)

                # 输出详细进度信息
                stats = self.performance_monitor.get_stats()
                logger.info(f"处理进度: {current}/{total} 批次, "
                          f"成功率: {stats['success_rate']:.1f}%, "
                          f"处理速度: {stats['processing_rate']:.1f} 项/秒, "
                          f"内存使用: {stats['current_memory_gb']:.1f}GB")

            # 使用优化的批量添加方法
            success = self.rag_system.batch_add_knowledge(
                knowledge_list=knowledge_list,
                batch_size=None,  # 使用自动计算的批处理大小
                progress_callback=progress_wrapper,
                enable_smart_batching=True
            )

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            # 输出最终统计
            final_stats = self.performance_monitor.get_stats()
            logger.info("=" * 60)
            logger.info("智能批量处理完成统计:")
            logger.info(f"  总处理时间: {final_stats['elapsed_time']:.2f} 秒")
            logger.info(f"  处理项目数: {final_stats['processed_items']}")
            logger.info(f"  失败项目数: {final_stats['failed_items']}")
            logger.info(f"  成功率: {final_stats['success_rate']:.1f}%")
            logger.info(f"  平均处理速度: {final_stats['processing_rate']:.1f} 项/秒")
            logger.info(f"  峰值内存使用: {final_stats['peak_memory_gb']:.1f}GB")
            logger.info("=" * 60)

            return success

        except Exception as e:
            logger.error(f"智能批量处理失败: {e}")
            self.performance_monitor.stop_monitoring()
            return False

    def batch_process_pdfs_smart(self, pdf_files: List[str],
                                category: str = "金融文档",
                                extract_tables: bool = True,
                                extract_images: bool = True,
                                progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        智能批量处理PDF文件

        Args:
            pdf_files: PDF文件路径列表
            category: 文档分类
            extract_tables: 是否提取表格
            extract_images: 是否提取图像
            progress_callback: 进度回调函数

        Returns:
            处理结果统计
        """
        try:
            if not pdf_files:
                logger.warning("PDF文件列表为空")
                return {"success": False, "message": "文件列表为空"}

            if not self.pdf_processor:
                logger.error("PDF处理器未初始化")
                return {"success": False, "message": "PDF处理器未初始化"}

            logger.info(f"开始智能批量处理 {len(pdf_files)} 个PDF文件")

            # 启动性能监控
            self.performance_monitor.start_monitoring()

            results = {
                "total_files": len(pdf_files),
                "successful_files": 0,
                "failed_files": 0,
                "total_knowledge_items": 0,
                "processing_time": 0,
                "failed_file_list": [],
                "success": False
            }

            start_time = time.time()

            # 根据系统配置决定是否并行处理
            if self.batch_config.max_workers > 1 and len(pdf_files) > 1:
                # 并行处理
                results = self._process_pdfs_parallel(
                    pdf_files, category, extract_tables, extract_images,
                    progress_callback, results
                )
            else:
                # 串行处理
                results = self._process_pdfs_sequential(
                    pdf_files, category, extract_tables, extract_images,
                    progress_callback, results
                )

            # 计算总处理时间
            results["processing_time"] = time.time() - start_time
            results["success"] = results["successful_files"] > 0

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            # 输出最终统计
            final_stats = self.performance_monitor.get_stats()
            logger.info("=" * 60)
            logger.info("PDF批量处理完成统计:")
            logger.info(f"  总文件数: {results['total_files']}")
            logger.info(f"  成功处理: {results['successful_files']}")
            logger.info(f"  处理失败: {results['failed_files']}")
            logger.info(f"  成功率: {(results['successful_files']/results['total_files'])*100:.1f}%")
            logger.info(f"  总知识项: {results['total_knowledge_items']}")
            logger.info(f"  总处理时间: {results['processing_time']:.2f} 秒")
            logger.info(f"  平均每文件: {results['processing_time']/results['total_files']:.2f} 秒")
            logger.info(f"  峰值内存使用: {final_stats['peak_memory_gb']:.1f}GB")
            if results['failed_file_list']:
                logger.warning(f"  失败文件: {', '.join(results['failed_file_list'])}")
            logger.info("=" * 60)

            return results

        except Exception as e:
            logger.error(f"PDF批量处理失败: {e}")
            self.performance_monitor.stop_monitoring()
            return {"success": False, "message": f"处理失败: {e}"}

    def _process_pdfs_parallel(self, pdf_files: List[str], category: str,
                              extract_tables: bool, extract_images: bool,
                              progress_callback: Optional[Callable],
                              results: Dict[str, Any]) -> Dict[str, Any]:
        """并行处理PDF文件"""
        try:
            logger.info(f"使用并行处理模式，工作线程数: {self.batch_config.max_workers}")

            def process_single_pdf(pdf_file: str) -> Dict[str, Any]:
                """处理单个PDF文件"""
                try:
                    if not os.path.exists(pdf_file):
                        return {"success": False, "file": pdf_file, "error": "文件不存在"}

                    # 处理PDF
                    success = self.pdf_processor.process_pdf_to_knowledge_base(
                        file_path=pdf_file,
                        category=category,
                        extract_tables=extract_tables,
                        extract_images=extract_images,
                        skip_deduplication=False
                    )

                    if success:
                        return {"success": True, "file": pdf_file}
                    else:
                        return {"success": False, "file": pdf_file, "error": "处理失败"}

                except Exception as e:
                    return {"success": False, "file": pdf_file, "error": str(e)}

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=self.batch_config.max_workers) as executor:
                futures = {executor.submit(process_single_pdf, pdf_file): pdf_file
                          for pdf_file in pdf_files}

                completed = 0
                for future in as_completed(futures):
                    pdf_file = futures[future]
                    result = future.result()
                    completed += 1

                    if result["success"]:
                        results["successful_files"] += 1
                        logger.info(f"成功处理: {pdf_file}")
                    else:
                        results["failed_files"] += 1
                        results["failed_file_list"].append(pdf_file)
                        logger.error(f"处理失败: {pdf_file} - {result.get('error', '未知错误')}")

                    # 更新进度
                    if progress_callback:
                        progress_callback(completed, len(pdf_files), results["successful_files"], len(pdf_files))

                    # 输出进度
                    progress_pct = (completed / len(pdf_files)) * 100
                    logger.info(f"并行处理进度: {progress_pct:.1f}% ({completed}/{len(pdf_files)})")

            return results

        except Exception as e:
            logger.error(f"并行处理PDF失败: {e}")
            return results

    def _process_pdfs_sequential(self, pdf_files: List[str], category: str,
                                extract_tables: bool, extract_images: bool,
                                progress_callback: Optional[Callable],
                                results: Dict[str, Any]) -> Dict[str, Any]:
        """串行处理PDF文件"""
        try:
            logger.info("使用串行处理模式")

            for i, pdf_file in enumerate(pdf_files):
                try:
                    if not os.path.exists(pdf_file):
                        logger.error(f"文件不存在: {pdf_file}")
                        results["failed_files"] += 1
                        results["failed_file_list"].append(pdf_file)
                        continue

                    logger.info(f"处理文件 {i+1}/{len(pdf_files)}: {pdf_file}")

                    # 处理PDF
                    success = self.pdf_processor.process_pdf_to_knowledge_base(
                        file_path=pdf_file,
                        category=category,
                        extract_tables=extract_tables,
                        extract_images=extract_images,
                        skip_deduplication=False
                    )

                    if success:
                        results["successful_files"] += 1
                        logger.info(f"成功处理: {pdf_file}")
                    else:
                        results["failed_files"] += 1
                        results["failed_file_list"].append(pdf_file)
                        logger.error(f"处理失败: {pdf_file}")

                    # 更新进度
                    if progress_callback:
                        progress_callback(i+1, len(pdf_files), results["successful_files"], len(pdf_files))

                    # 输出进度
                    progress_pct = ((i+1) / len(pdf_files)) * 100
                    logger.info(f"串行处理进度: {progress_pct:.1f}% ({i+1}/{len(pdf_files)})")

                    # 检查内存使用情况，必要时进行垃圾回收
                    if (i + 1) % 10 == 0:  # 每处理10个文件检查一次
                        import gc
                        gc.collect()

                except Exception as e:
                    logger.error(f"处理文件失败 {pdf_file}: {e}")
                    results["failed_files"] += 1
                    results["failed_file_list"].append(pdf_file)

            return results

        except Exception as e:
            logger.error(f"串行处理PDF失败: {e}")
            return results

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "system_info": {
                "total_memory_gb": self.system_info.total_memory_gb,
                "available_memory_gb": self.system_info.available_memory_gb,
                "cpu_count": self.system_info.cpu_count,
                "gpu_available": self.system_info.gpu_available,
                "gpu_memory_gb": self.system_info.gpu_memory_gb,
                "gpu_name": self.system_info.gpu_name
            },
            "batch_config": {
                "batch_size": self.batch_config.batch_size,
                "embedding_batch_size": self.batch_config.embedding_batch_size,
                "max_workers": self.batch_config.max_workers,
                "cache_size": self.batch_config.cache_size,
                "enable_gpu": self.batch_config.enable_gpu,
                "memory_threshold": self.batch_config.memory_threshold
            }
        }

    def optimize_for_large_dataset(self, estimated_items: int) -> None:
        """为大数据集优化配置"""
        try:
            logger.info(f"为大数据集优化配置，预估项目数: {estimated_items}")

            if estimated_items > 10000:
                # 超大数据集：减少批处理大小，增加缓存
                self.batch_config.batch_size = max(20, self.batch_config.batch_size // 2)
                self.batch_config.embedding_batch_size = max(8, self.batch_config.embedding_batch_size // 2)
                self.batch_config.cache_size = min(5000, self.batch_config.cache_size * 2)
                logger.info("应用超大数据集优化配置")

            elif estimated_items > 5000:
                # 大数据集：适度调整
                self.batch_config.batch_size = max(30, int(self.batch_config.batch_size * 0.8))
                self.batch_config.cache_size = min(3000, int(self.batch_config.cache_size * 1.5))
                logger.info("应用大数据集优化配置")

            # 更新RAG系统配置
            if self.rag_system:
                self.rag_system.batch_size = self.batch_config.batch_size
                self.rag_system.embedding_batch_size = self.batch_config.embedding_batch_size
                self.rag_system.cache_size_limit = self.batch_config.cache_size

            logger.info(f"优化后配置: batch_size={self.batch_config.batch_size}, "
                       f"embedding_batch_size={self.batch_config.embedding_batch_size}, "
                       f"cache_size={self.batch_config.cache_size}")

        except Exception as e:
            logger.error(f"大数据集优化失败: {e}")
