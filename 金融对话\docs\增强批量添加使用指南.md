# 增强批量添加功能使用指南

## 概述

本系统在原有批量添加功能基础上进行了全面增强，主要特性包括：

- **🚀 性能优化**: 智能系统检测和自动配置优化
- **📊 进度可视化**: 支持tqdm进度条和自定义进度回调
- **💾 智能缓存**: 线程安全的嵌入向量缓存机制
- **⚡ 并行处理**: 多线程并行批处理
- **🔧 灵活配置**: 速度优化和内存优化模式
- **📈 统计监控**: 详细的性能统计和缓存分析

## 快速开始

### 1. 安装依赖

```bash
pip install -r idconfig/requirements.txt
```

新增依赖：
- `tqdm>=4.64.0` - 进度条显示
- `psutil>=5.8.0` - 系统信息获取

### 2. 基本使用

```python
from rag.rag_system import RAGSystem

# 初始化系统
rag_system = RAGSystem()
rag_system.initialize()

# 准备知识数据
knowledge_list = [
    {
        "content": "股票是公司所有权的凭证...",
        "category": "股票基础",
        "source": "金融教材"
    },
    # 更多知识项...
]

# 执行增强批量添加
success = rag_system.batch_add_knowledge(
    knowledge_list=knowledge_list,
    show_progress=True,        # 显示进度条
    enable_cache=True,         # 启用缓存
    enable_smart_batching=True # 智能批处理
)
```

## 核心功能

### 1. 进度可视化

#### tqdm进度条
当安装了tqdm库时，系统会自动显示美观的进度条：

```
批量添加知识: 75%|███████▌  | 750/1000 [00:30<00:10, 25.0items/s] success=745, failed=5
```

#### 简单进度显示
如果tqdm不可用，会使用日志形式的进度显示：

```
批量添加知识: 750/1000 (75.0%) - 25.0 items/s - success=745, failed=5
```

#### 自定义进度回调
```python
def progress_callback(current_batch, total_batches, success_count, failed_count):
    progress = (current_batch / total_batches) * 100
    print(f"进度: {progress:.1f}% - 成功: {success_count}, 失败: {failed_count}")

rag_system.batch_add_knowledge(
    knowledge_list=knowledge_list,
    progress_callback=progress_callback
)
```

### 2. 智能缓存系统

#### 自动缓存管理
- **线程安全**: 使用锁机制保证多线程安全
- **自动清理**: 超过限制时自动清理最旧的缓存项
- **统计监控**: 实时统计缓存命中率

#### 缓存操作
```python
# 获取缓存统计
cache_stats = rag_system.get_cache_stats()
print(f"缓存大小: {cache_stats['cache_size']}")
print(f"命中率: {cache_stats['hit_rate']:.1f}%")

# 清空缓存
rag_system.clear_cache()
```

### 3. 性能优化模式

#### 速度优化模式
```python
rag_system.optimize_for_speed()
```
- 增大批处理大小
- 增大嵌入向量批处理大小
- 增大缓存容量
- 增加并行线程数

#### 内存优化模式
```python
rag_system.optimize_for_memory()
```
- 减小批处理大小
- 减小缓存容量
- 减少并行线程数
- 清空现有缓存

### 4. 系统自动优化

系统会根据硬件配置自动优化：

```python
# 自动检测并优化配置
rag_system.optimize_batch_settings()
```

检测内容：
- **内存大小**: 调整批处理大小和缓存容量
- **CPU核心数**: 设置合适的并行线程数
- **GPU信息**: 优化嵌入向量批处理大小
- **可用内存**: 动态调整处理策略

## 使用示例

### 示例1: 基本批量添加

```python
from rag.rag_system import RAGSystem

# 初始化
rag_system = RAGSystem()
rag_system.initialize()

# 创建测试数据
knowledge_list = [
    {
        "content": f"测试知识条目 #{i+1}",
        "category": "测试",
        "source": f"测试来源 #{i+1}"
    }
    for i in range(1000)
]

# 执行批量添加
success = rag_system.batch_add_knowledge(
    knowledge_list=knowledge_list,
    show_progress=True,
    enable_cache=True
)

print(f"批量添加结果: {success}")
```

### 示例2: 性能优化对比

```python
import time

# 创建测试数据
test_data = [...]  # 你的知识数据

# 测试默认模式
rag_system = RAGSystem()
rag_system.initialize()

start_time = time.time()
rag_system.batch_add_knowledge(test_data, show_progress=True)
default_time = time.time() - start_time

# 测试速度优化模式
rag_system.optimize_for_speed()

start_time = time.time()
rag_system.batch_add_knowledge(test_data, show_progress=True)
optimized_time = time.time() - start_time

print(f"默认模式: {default_time:.2f}s")
print(f"优化模式: {optimized_time:.2f}s")
print(f"加速比: {default_time/optimized_time:.2f}x")
```

### 示例3: 缓存效果验证

```python
# 创建包含重复内容的数据
repeated_data = []
base_content = "这是重复的内容"

for i in range(100):
    repeated_data.append({
        "content": base_content,
        "category": "重复测试",
        "source": f"来源 #{i+1}"
    })

# 第一次处理（建立缓存）
start_time = time.time()
rag_system.batch_add_knowledge(repeated_data, show_progress=True)
first_time = time.time() - start_time
first_stats = rag_system.get_cache_stats()

# 第二次处理（利用缓存）
start_time = time.time()
rag_system.batch_add_knowledge(repeated_data, show_progress=True)
second_time = time.time() - start_time
second_stats = rag_system.get_cache_stats()

print(f"第一次: {first_time:.2f}s, 命中率: {first_stats['hit_rate']:.1f}%")
print(f"第二次: {second_time:.2f}s, 命中率: {second_stats['hit_rate']:.1f}%")
```

## 配置参数

### 批处理配置
- `batch_size`: 知识库批处理大小（默认50）
- `embedding_batch_size`: 嵌入向量批处理大小（默认16）
- `max_workers`: 最大并行线程数（默认4）

### 缓存配置
- `cache_size_limit`: 缓存大小限制（默认1000）
- `enable_cache`: 是否启用缓存（默认True）

### 重试配置
- `retry_attempts`: 重试次数（默认3）
- `retry_delay`: 重试延迟秒数（默认1.0）

## 性能监控

### 获取系统信息
```python
system_info = rag_system.get_system_info()
print(f"批处理配置: {system_info}")
```

### 获取缓存统计
```python
cache_stats = rag_system.get_cache_stats()
print(f"缓存统计: {cache_stats}")
```

### 性能基准测试
```python
# 运行演示脚本
python examples/enhanced_batch_demo.py

# 运行测试脚本
python test_enhanced_batch.py
```

## 故障排除

### 常见问题

1. **进度条不显示**
   ```bash
   pip install tqdm
   ```

2. **系统优化不生效**
   ```bash
   pip install psutil
   ```

3. **内存不足**
   ```python
   rag_system.optimize_for_memory()
   ```

4. **处理速度慢**
   ```python
   rag_system.optimize_for_speed()
   ```

### 调试信息

启用详细日志：
```python
from loguru import logger
logger.add("debug.log", level="DEBUG")
```

查看缓存状态：
```python
cache_stats = rag_system.get_cache_stats()
logger.info(f"缓存统计: {cache_stats}")
```

## 最佳实践

1. **数据预处理**: 清理重复和无效数据
2. **合理配置**: 根据硬件选择优化模式
3. **监控性能**: 定期检查缓存命中率
4. **批量大小**: 根据内存情况调整批处理大小
5. **错误处理**: 监控失败率，必要时调整参数

## 更新日志

### v2.0 增强版本
- ✅ 添加tqdm进度条支持
- ✅ 实现线程安全的嵌入向量缓存
- ✅ 添加系统自动检测和优化
- ✅ 支持速度和内存优化模式
- ✅ 增强错误处理和重试机制
- ✅ 添加详细的性能统计
- ✅ 支持自定义进度回调
- ✅ 优化并行处理性能
